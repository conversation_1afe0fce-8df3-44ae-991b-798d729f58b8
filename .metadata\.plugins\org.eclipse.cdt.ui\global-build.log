17:38:13 **** Incremental Build of configuration Debug for project 48V code ****
make -j14 all 
'Building file: ../03_BSW/System/03_MCAL/main.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\Com\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Com\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Com\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -std=gnu11 -MMD -MP -MF"03_BSW/System/03_MCAL/main.d" -MT"03_BSW/System/03_MCAL/main.o" -c -o "03_BSW/System/03_MCAL/main.o" "../03_BSW/System/03_MCAL/main.c"
'Finished building: ../03_BSW/System/03_MCAL/main.c'
' '
'Building target: 48V code.elf'
'Invoking: GNU Arm Cross C Linker'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -T "D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M\GCC\Z20K116M_flash.ld" -Xlinker --gc-sections -Wl,-Map,"48V code.map" --specs=nosys.specs -o "48V code.elf"  ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.o  ./03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.o  ./03_BSW/System/03_MCAL/main.o  ./.metadata/.plugins/org.eclipse.cdt.make.core/specs.o   
d:/software/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/ld.exe: ./03_BSW/System/03_MCAL/main.o: in function `system_init':
D:\NN\Project\48V\48V code\Debug/../03_BSW/System/03_MCAL/main.c:72: undefined reference to `__vector_table'
collect2.exe: error: ld returned 1 exit status
make: *** [makefile:64: 48V code.elf] Error 1
"make -j14 all" terminated with exit code 2. Build might be incomplete.
