/*******************************************************************************
** Copyright (c) 2024 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : EcuM.h
** Module Name : ECU State Manager
** -----------------------------------------------------------------------------
**
** Description : Header file for ECU State Manager module
** This file contains the interface definitions for the EcuM module
** providing ECU state management and power mode control.
** -----------------------------------------------------------------------------
**
** Documentation reference : 04_System Module Interface Specification.md
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V00.01 07/29/2025
** - Baseline Created
**
*******************************************************************************/

#ifndef ECUM_H
#define ECUM_H

/*************************** Inclusion files **********************************/
#include "Std_Types.h"
#include "Mcu.h"
#include "Gpt.h"
#include "Wdg.h"
#include "Wdgif.h"
#include "Wdgm.h"
/********************** Component configuration *******************************/

/**************** Declaration of local symbol and constants *******************/

/******************** Declaration of local macros *****************************/

/********************* Declaration of local types *****************************/

/* ECU state enumeration */
typedef enum {
    ECUM_STATE_STARTUP = 0,                 /* ECU startup phase */
    ECUM_STATE_UP,                          /* Normal operation state */
    ECUM_STATE_SLEEP,                       /* ECU sleep mode */
    ECUM_STATE_SHUTDOWN                     /* ECU shutdown phase */
} EcuM_StateType;

/* ECU sleep source enumeration */
typedef enum {
    ECUM_SLEEP_SOURCE_NONE = 0,              /* No sleep source */
    ECUM_SLEEP_SOURCE_4S_TIMEOUT,            /* 4s LIN timeout sleep */
    ECUM_SLEEP_SOURCE_LIN_CMD                /* LIN command sleep */
} EcuM_SleepSourceType;

/******************* Declaration of local variables ***************************/

/********************* Declaration of local constants *************************/

/******************** Declaration of exported variables ***********************/

/****************** Declaration of exported constant **************************/

/*******************************************************************************
** FUNCTIONS **
*******************************************************************************/

/**************************************************************************************************
** Function name   : EcuM_Init
** Description     : ECU State Manager initialization
** Parameters      : None
** Return value    : None
** Notes           : This function initializes the ECU state manager
**************************************************************************************************/
FUNC(void, ECUM_CODE) EcuM_Init(void);

/**************************************************************************************************
** Function name   : EcuM_GetState
** Description     : Get current ECU state
** Parameters      : None
** Return value    : EcuM_StateType - Current ECU state
** Notes           : This function returns the current ECU state
**************************************************************************************************/
FUNC(EcuM_StateType, ECUM_CODE) EcuM_GetState(void);

/**************************************************************************************************
** Function name   : EcuM_SetState
** Description     : Set current ECU state
** Parameters      : state - ECU state to set
** Return value    : Std_ReturnType - E_OK: State set successfully
**                                   E_NOT_OK: State setting failed
** Notes           : This function sets the ECU state
**************************************************************************************************/
FUNC(Std_ReturnType, ECUM_CODE) EcuM_SetState(VAR(EcuM_StateType, ECUM_VAR) state);

/**************************************************************************************************
** Function name   : EcuM_InitActivity
** Description     : Initialization activity
** Parameters      : None
** Return value    : EcuM_StateType - Next ECU state
** Notes           : This function performs initialization checks (voltage, temperature)
**************************************************************************************************/
FUNC(EcuM_StateType, ECUM_CODE) EcuM_InitActivity(void);

/**************************************************************************************************
** Function name   : EcuM_RunActivity
** Description     : Run activity
** Parameters      : None
** Return value    : EcuM_StateType - Next ECU state
** Notes           : This function performs run activity operations
**************************************************************************************************/
FUNC(EcuM_StateType, ECUM_CODE) EcuM_RunActivity(void);

/**************************************************************************************************
** Function name   : EcuM_MonitorSleepSource
** Description     : Monitor sleep source
** Parameters      : None
** Return value    : EcuM_SleepSourceType - Sleep source type
** Notes           : This function monitors the sleep source
**************************************************************************************************/
FUNC(EcuM_SleepSourceType, ECUM_CODE) EcuM_MonitorSleepSource(void);

/**************************************************************************************************
** Function name   : EcuM_ValidateWakeupEvent
** Description     : Wake Up Event Monitoring
** Parameters      : None
** Return value    : Std_ReturnType - E_OK: Wakeup event valid
**                                   E_NOT_OK: No valid wakeup event
** Notes           : This function validates wakeup events
**************************************************************************************************/
FUNC(Std_ReturnType, ECUM_CODE) EcuM_ValidateWakeupEvent(void);

/**************************************************************************************************
** Function name   : EcuM_ClearWakeupEvent
** Description     : Clear wakeup event
** Parameters      : None
** Return value    : Std_ReturnType - E_OK: Wakeup event cleared
**                                   E_NOT_OK: Clear operation failed
** Notes           : This function clears wakeup events
**************************************************************************************************/
FUNC(Std_ReturnType, ECUM_CODE) EcuM_ClearWakeupEvent(void);

/**************************************************************************************************
** Function name   : EcuM_SleepActivity
** Description     : Sleep activity
** Parameters      : None
** Return value    : EcuM_StateType - Next ECU state
** Notes           : This function performs sleep activity operations
**************************************************************************************************/
FUNC(EcuM_StateType, ECUM_CODE) EcuM_SleepActivity(void);

/**************************************************************************************************
** Function name   : EcuM_ShutdownActivity
** Description     : Shutdown activity
** Parameters      : None
** Return value    : EcuM_StateType - Next ECU state
** Notes           : This function performs shutdown activity operations
**************************************************************************************************/
FUNC(EcuM_StateType, ECUM_CODE) EcuM_ShutdownActivity(void);



/**************************************************************************************************
** Function name   : EcuM_MainFunction
** Description     : Main function for ECU state management
** Parameters      : None
** Return value    : None
** Notes           : This function performs ECU state management tasks
**************************************************************************************************/
FUNC(void, ECUM_CODE) EcuM_MainFunction(void);

#endif /* ECUM_H */
