/*******************************************************************************
** Copyright (c) 2018 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
**
** -----------------------------------------------------------------------------
** File Name    : RTSC.h
** Module Name  : RTSC
** -----------------------------------------------------------------------------
**
** Description : Include file of component RTSC.c
** This file must exclusively contain informations needed to
** use this component.
**
** -----------------------------------------------------------------------------
**
** Documentation reference : EME-19ST004-12232  (SW_LLD_RTSC)
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V01.00 S.Todkar 30/04/2019
** - Baseline Created
*******************************************************************************/
/* To avoid multi-inclusions */
#ifndef RTSC_CODE
#define RTSC_CODE

/*************************** Inclusion files **********************************/
#include "Std_Types.h"
#include "TIMT.h"

/**************** Declaration of global symbol and constants ******************/

/******************** Declaration of global macros ****************************/
/********************* Declaration of global types ****************************/

/******************** External links of global variables **********************/

/******************** External links of global constant ***********************/

/*******************************************************************************
** FUNCTIONS **
*******************************************************************************/
/************************** Function definitions ******************************/

/*******************************************************************************
** Function name    : RTSC_Start
** Description      : SScheduler Start
** Parameter index : Description
** Return value     : None
** Remarks          : global variables used, side effects
*******************************************************************************/
EXTERN FUNC(void, RTSC_CODE) RTSC_Start(void);

/*******************************************************************************
** Function name    : RTSCU_Scheduler
** Description      : Scheduler timer ISR call back function
** Parameter index : Description
** Return value     : None
** Remarks          : global variables used, side effects
*******************************************************************************/
EXTERN FUNC(void, RTSC_CODE) RTSC_Scheduler(void);

/*******************************************************************************
** Function name    : RTSC_GetSchedOverLoadSt
** Description      : It provides the scheduler overload status
** Parameter index  : None
** Return value             : RTSC_iSchedOverload
** Remarks                  : None
*******************************************************************************/
EXTERN FUNC(boolean, RTSC_CODE) RTSC_GetSchedOverLoadSt(void);

#endif /* RTSC_CODE */