/*******************************************************************************
** Copyright (c) 2024 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : Sysytem
** Module Name : System start
** -----------------------------------------------------------------------------
**
** Description : Configuration file of component WDOG
** This file must exclusively contain informations needed to
** use this component.
** -----------------------------------------------------------------------------
**
** Documentation reference : 
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V01.00 NZR 05/20/2024
** - Baseline Created
**
*******************************************************************************/
#ifndef SYSTEM_H
#define SYSTEM_H
/*************************** Inclusion files **********************************/
#include "platform_cfg.h"	/* Include header file containing MCU platform configuration information */
#include "Z20K11xM_drv.h"	/* Include header file containing commonly used information in the SDK */
#include "Z20k11xM_pmu.h"
#include "Z20k11xM_clock.h"
#include "Z20k11xM_sysctrl.h"
#include "Z20k11xM_flash.h"  
#include "Z20k11xM_gpio.h"
#include "Z20k11xM_wdog.h"
#include "Z20k11xM_stim.h"
#include "Z20K11xM_hwdiv.h"

#include "CLKT.h"
#include "TIMT.h"

#include "RTSC.h"


/************************** Declaration of global symbol and constants ****************************/

/********************************* Declaration of global macros ***********************************/

/********************************* Declaration of global types ************************************/

/****************************** External links of global variables ********************************/

/****************************** External links of global constants ********************************/

/***************************************************************************************************
**                                      FUNCTIONS                                                 **
***************************************************************************************************/

/********************************** Function definitions ******************************************/
/*****************************************************************************
* Function: main1
* Description: main function
* Parameters: None
* Returns: None
* Note: None
****************************************************************************/
EXTERN FUNC(void, SYS_CODE) main1(void);
#endif
