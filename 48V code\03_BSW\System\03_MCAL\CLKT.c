/*******************************************************************************
** Copyright (c) 2024 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : CLKT.c
** Module Name : CLKT
** -----------------------------------------------------------------------------
**
** Description : Configuration file of component Clock
** 
** -----------------------------------------------------------------------------
**
** Documentation reference : SW_LLD_CLKT
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V01.00 NZR 05/20/2024
** - Baseline Created
**
*******************************************************************************/

/*************************** Inclusion files **********************************/
#include "CLKT.h"
/********************** Component configuration *******************************/

/**************** Declaration of local symbol and constants *******************/

/******************** Declaration of local macros *****************************/

/********************* Declaration of local types *****************************/

/******************* Declaration of local variables ***************************/

/********************* Declaration of local constants *************************/

/******************** Declaration of exported variables ***********************/

/* Timer count variable and flag bit for normal operations */

/****************** Declaration of exported constant **************************/

/*******************************************************************************
** FUNCTIONS **
*******************************************************************************/
/************************** Function definitions ******************************/
/*****************************************************************************
 * Function: CLKT_Init
 * Description: Configure the clock module (SCC).
 * Parameters:
 * Returns:
 * Remarks: 1. The bus clock uses the internal 64M FIRC to ensure the stability of the bus clock;
 *              enable the external crystal oscillator to provide a clock source for modules such as CAN,
 *              ensuring the accuracy of the module clock.
 *          2. When reading FLASH, the core clock is used. The clock of the FLASH controller is CLK_SLOW (Flash_CLK),
 *              with a typical value of 8Mhz.
 *          3. For the system clock, refer to "Peripheral clock summary".
 ****************************************************************************/
FUNC(void, CLKT_CODE) CLKT_Init(void)
{
    /* Initialize the CLOCK module */
    CLK_FIRC64MEnable(DISABLE);     /* Enable FIRC64M and disable running in STOP mode */
    CLK_LPO32KEnable();             /* Enable the internal 32K clock source */
    CLK_OSC40MEnable2(CLK_OSC_FREQ_MODE_LOW, DISABLE, CLK_OSC_XTAL);// Enable the external 16M crystal oscillator
    /* The maximum read speed of FLASH is 32MHz due to the physical characteristics of the material.
       FLASH is connected to the core clock bus, so the clock needs to be reasonably matched. */
    /* FLASH_SetWaitState(0);   When 0M < core clock <= 32M, read data from FLASH, add 0 clock wait time. */
    FLASH_SetWaitState(1);          /* When 32M < core clock <= 64M, read data from FLASH, add 1 clock wait time. */
    
    CLK_SysClkSrc(CLK_SYS_FIRC64M);            /* Select FIRC64M as the clock source for the system clock */
    CLK_SetClkDivider(CLK_CORE, CLK_DIV_1);    /* Set the clock divider for the core clock */
    CLK_SetClkDivider(CLK_BUS, CLK_DIV_2);     /* Set the clock divider for the bus clock. Considering functional reliability, set the bus clock to half of the core clock. */
    CLK_SetClkDivider(CLK_SLOW, CLK_DIV_8);    /* Set the clock divider for the FLASH controller. The typical value of the FLASH controller clock is 8Mhz. */
    CLK_ClkOutDisable();                       /* Disable clock output */
    
    /* Initialize CLOCK interrupts */
    SCC_IntClear(CLK_INT_ALL);  /* Clear all CLOCK interrupt flags */

    NVIC_DisableIRQ(SCC_IRQn);  /* Disable the SCC_IRQn interrupt */
    
    /* Initialize variables */
    
    /* Start the module, the module starts running */
}