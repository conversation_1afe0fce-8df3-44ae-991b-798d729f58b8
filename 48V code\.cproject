<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="ilg.gnuarmeclipse.managedbuild.cross.config.elf.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="ilg.gnuarmeclipse.managedbuild.cross.config.elf.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="${cross_rm} -rf" description="" errorParsers="org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.GLDErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser" id="ilg.gnuarmeclipse.managedbuild.cross.config.elf.debug.**********" name="Debug" optionalBuildProperties="org.eclipse.cdt.docker.launcher.containerbuild.property.enablement=null,org.eclipse.cdt.docker.launcher.containerbuild.property.selectedvolumes=,org.eclipse.cdt.docker.launcher.containerbuild.property.volumes=,org.eclipse.cdt.docker.launcher.containerbuild.property.image=null,org.eclipse.cdt.docker.launcher.containerbuild.property.connection=null" parent="ilg.gnuarmeclipse.managedbuild.cross.config.elf.debug">
					<folderInfo id="ilg.gnuarmeclipse.managedbuild.cross.config.elf.debug.**********." name="/" resourcePath="">
						<toolChain id="ilg.gnuarmeclipse.managedbuild.cross.toolchain.elf.debug.1359102909" name="Arm Cross GCC" superClass="ilg.gnuarmeclipse.managedbuild.cross.toolchain.elf.debug">
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash.303073552" name="Create flash image" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting.1464288155" name="Create extended listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize.1858932428" name="Print size" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.710595784" name="Optimization Level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level" value="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.none" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength.1102307340" name="Message length (-fmessage-length=0)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar.1940273080" name="'char' is signed (-fsigned-char)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections.611004681" name="Function sections (-ffunction-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections.1462833082" name="Data sections (-fdata-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.651554525" name="Debug level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level" value="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.max" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format.1435167045" name="Debug format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.id.252742728" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.id" value="1287942917" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name.524399541" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name" value="GNU Tools for ARM Embedded Processors" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.1957701076" name="Architecture" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.architecture" value="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.arm" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family.1659975330" name="Arm family (-mcpu)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.mcpu.cortex-m0plus" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.811833230" name="Instruction set" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.thumb" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix.673900116" name="Prefix" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.c.1712531703" name="C compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.c" value="gcc" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp.789554246" name="C++ compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp" value="g++" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar.690681867" name="Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar" value="ar" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy.1833188682" name="Hex/Bin converter" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy" value="objcopy" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump.485540633" name="Listing generator" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump" value="objdump" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.size.784137788" name="Size command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.size" value="size" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.make.763483763" name="Build command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.make" value="make" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm.149905787" name="Remove command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm" value="rm" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.endianness.146286795" name="Endianness" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.endianness" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.endianness.little" valueType="enumerated"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.GNU_ELF" id="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform.532413384" isAbstract="false" osList="all" superClass="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform"/>
							<builder buildPath="${workspace_loc:/48V code}/Debug" id="ilg.gnuarmeclipse.managedbuild.cross.builder.386304985" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="ilg.gnuarmeclipse.managedbuild.cross.builder"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.682025191" name="GNU Arm Cross Assembler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.usepreprocessor.547511756" name="Use preprocessor" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.usepreprocessor" value="true" valueType="boolean"/>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input.1246454793" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.757882940" name="GNU Arm Cross C Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.include.paths.1887068108" name="Include paths (-I)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/ZhiXinSDK/MCU}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/ZhiXinSDK/Platform}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/ZhiXinSDK/Platform/Core}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/ZhiXinSDK/Platform/Z20K116M}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/ZhiXinSDK/StdDriver/Inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/ZhiXinSDK/StdDriver/Src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/Com/01_Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/Com/02_HAL}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/Com/03_MCAL}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/IO/02_HAL}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/IO/03_MCAL}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/Memory/01_Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/Memory/02_HAL}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/Memory/03_MCAL}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/STAR}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/System/01_Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/System/02_HAL}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/System/03_MCAL}&quot;"/>
								</option>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.1314529434" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler.1275731701" name="GNU Arm Cross C++ Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.1773659286" name="GNU Arm Cross C Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.gcsections.1869650505" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.gcsections" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.usenewlibnosys.1236740873" name="Do not use syscalls (--specs=nosys.specs)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.usenewlibnosys" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.scriptfile.265294041" name="Script files (-T)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_flash.ld}&quot;"/>
								</option>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.input.1198315333" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker.7083792" name="GNU Arm Cross C++ Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.gcsections.1949293405" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.gcsections" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver.1553035726" name="GNU Arm Cross Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash.1134863396" name="GNU Arm Cross Create Flash Image" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting.1793472414" name="GNU Arm Cross Create Listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.source.1375999719" name="Display source (--source|-S)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.allheaders.846370153" name="Display all headers (--all-headers|-x)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.demangle.906034509" name="Demangle names (--demangle|-C)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.linenumbers.1054671897" name="Display line numbers (--line-numbers|-l)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.wide.288381958" name="Wide lines (--wide|-w)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize.821735392" name="GNU Arm Cross Print Size" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.printsize.format.**********" name="Size format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.printsize.format"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="ilg.gnuarmeclipse.managedbuild.cross.config.elf.release.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="ilg.gnuarmeclipse.managedbuild.cross.config.elf.release.**********" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="${cross_rm} -rf" description="" id="ilg.gnuarmeclipse.managedbuild.cross.config.elf.release.**********" name="Release" optionalBuildProperties="" parent="ilg.gnuarmeclipse.managedbuild.cross.config.elf.release">
					<folderInfo id="ilg.gnuarmeclipse.managedbuild.cross.config.elf.release.**********." name="/" resourcePath="">
						<toolChain id="ilg.gnuarmeclipse.managedbuild.cross.toolchain.elf.release.1855305023" name="Arm Cross GCC" superClass="ilg.gnuarmeclipse.managedbuild.cross.toolchain.elf.release">
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash.1816051231" name="Create flash image" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting.1298199628" name="Create extended listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize.1312669603" name="Print size" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.697387488" name="Optimization Level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level" value="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.size" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength.1463250441" name="Message length (-fmessage-length=0)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar.441673174" name="'char' is signed (-fsigned-char)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections.608528350" name="Function sections (-ffunction-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections.949792979" name="Data sections (-fdata-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.1025139045" name="Debug level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format.1422105708" name="Debug format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.id.1809619050" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.id" value="1287942917" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name.1564963454" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name" value="GNU Tools for ARM Embedded Processors" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.567071247" name="Architecture" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.architecture" value="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.arm" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family.502062696" name="Arm family (-mcpu)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.mcpu.cortex-m3" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.151198039" name="Instruction set" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.thumb" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix.1135205339" name="Prefix" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.c.1179465772" name="C compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.c" value="gcc" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp.1454755822" name="C++ compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp" value="g++" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar.477448618" name="Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar" value="ar" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy.651141050" name="Hex/Bin converter" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy" value="objcopy" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump.264567402" name="Listing generator" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump" value="objdump" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.size.489607067" name="Size command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.size" value="size" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.make.441528217" name="Build command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.make" value="make" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm.911266930" name="Remove command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm" value="rm" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.GNU_ELF" id="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform.628024689" isAbstract="false" osList="all" superClass="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform"/>
							<builder buildPath="${workspace_loc:/48V code}/Release" id="ilg.gnuarmeclipse.managedbuild.cross.builder.1460435507" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" superClass="ilg.gnuarmeclipse.managedbuild.cross.builder"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.762182383" name="GNU Arm Cross Assembler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.usepreprocessor.1930714439" name="Use preprocessor" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.usepreprocessor" value="true" valueType="boolean"/>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input.1906908570" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.**********" name="GNU Arm Cross C Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler">
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.208555359" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler.115673930" name="GNU Arm Cross C++ Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.234263483" name="GNU Arm Cross C Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.gcsections.1791990498" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.gcsections" value="true" valueType="boolean"/>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.input.906846221" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker.286000202" name="GNU Arm Cross C++ Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.gcsections.98552598" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.gcsections" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver.172425720" name="GNU Arm Cross Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash.942315079" name="GNU Arm Cross Create Flash Image" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting.993160595" name="GNU Arm Cross Create Listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.source.1886364166" name="Display source (--source|-S)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.allheaders.803238109" name="Display all headers (--all-headers|-x)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.demangle.952816762" name="Demangle names (--demangle|-C)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.linenumbers.1493030835" name="Display line numbers (--line-numbers|-l)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.wide.962846338" name="Wide lines (--wide|-w)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize.2085731573" name="GNU Arm Cross Print Size" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.printsize.format.1601002661" name="Size format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.printsize.format"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="48V code.ilg.gnuarmeclipse.managedbuild.cross.target.elf.356008425" name="Executable" projectType="ilg.gnuarmeclipse.managedbuild.cross.target.elf"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="ilg.gnuarmeclipse.managedbuild.cross.config.elf.debug.**********;ilg.gnuarmeclipse.managedbuild.cross.config.elf.debug.**********.;ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.757882940;ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.1314529434">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnuarmeclipse.managedbuild.cross.config.elf.release.**********;ilg.gnuarmeclipse.managedbuild.cross.config.elf.release.**********.;ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.**********;ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.208555359">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/48V code"/>
		</configuration>
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/48V code"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
</cproject>