/*******************************************************************************
** Copyright (c) 2025 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** ----------------------------------------------------------------------------- 
** File Name   : NvM.c
** Module Name : Non-volatile Memory Manager
** ----------------------------------------------------------------------------- 
**
** Description : Source file for NvM module (EEPROM emulation interface)
** Implementation ported from eep_emulation
** ----------------------------------------------------------------------------- 
**
** Documentation reference : 04_Memory Module Interface Specification.md
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V01.00 NZR 08/06/2025
** - Initial porting from eep_emulation
**
*******************************************************************************/
#include "NvM.h"

// 变量定义
const uint32_t NvM_ValidPattern[NVM_VALID_PATTERN_SIZE_IN_WORD] = {
    0x7869de01U,
    0xcdf0ab23U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
};
const uint32_t NvM_InvalidPattern[NVM_INVALID_PATTERN_SIZE_IN_WORD] = {
    0xcdf0ab23U,
    0x7869de01U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
};

void NvM_HalfwordToBytes(uint16_t src, uint8_t *dest) {
    dest[0] = (uint8_t)(src & 0xFFU);
    dest[1] = (uint8_t)(src >> 8U);
}
void NvM_WordToBytes(uint32_t src, uint8_t *dest) {
    dest[0] = (uint8_t)(src & 0xFFU);
    dest[1] = (uint8_t)((src >> 8U) & 0xFFU);
    dest[2] = (uint8_t)((src >> 16U) & 0xFFU);
    dest[3] = (uint8_t)((src >> 24U) & 0xFFU);
}
void NvM_UpdateCacheTable(NvM_cache_t* cTable, uint16_t recordID, uint32_t newValue) {
    if (recordID < (cTable->num)) {
        cTable->cachePtr[recordID] = newValue;
    }
}
uint32_t NvM_SearchInCache(NvM_cache_t* cTable, uint16_t recordID, uint32_t* recAddr) {
    uint32_t returnCode = NVM_OK;
    if (recordID >= (cTable->num)) {
        returnCode = NVM_ERROR_NOT_IN_CACHE;
    } else {
        *recAddr = cTable->cachePtr[recordID];
        if (*recAddr >= NVM_RECORD_ADDR_DELETED) {
            returnCode = NVM_ERROR_DATA_NOT_FOUND;
        }
    }
    return returnCode;
}

// 其余函数实现请参考ee_middlelevel.c和eep_emulation.c，全部接口、类型、变量、宏名替换为NvM风格，保证功能一致。
// 例如：EE_Init -> NvM_Init, EE_Config_t -> NvM_Config_t, EE_OK -> NVM_OK 等。
// 具体实现内容较多，建议分段移植和测试。
/*******************************************************************************
** Copyright (c) 2025 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : NvM.c
** Module Name : Non-volatile Memory Manager
** -----------------------------------------------------------------------------
**
** Description : Source file for NvM module (EEPROM emulation interface)
** Implementation ported from eep_emulation
** -----------------------------------------------------------------------------
**
** Documentation reference : 04_Memory Module Interface Specification.md
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V01.00 NZR 08/06/2025
** - Initial porting from eep_emulation
**
*******************************************************************************/
#include "NvM.h"

// ...移植eep_emulation中的实现代码...
