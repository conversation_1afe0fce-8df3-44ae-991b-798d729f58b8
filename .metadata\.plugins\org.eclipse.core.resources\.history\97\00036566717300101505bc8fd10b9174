/*******************************************************************************
** Copyright (c) 2024 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : System
** Module Name : System start
** -----------------------------------------------------------------------------
**
** Description : Configuration file of component WDOG
** This file must exclusively contain informations needed to
** use this component.
** -----------------------------------------------------------------------------
**
** Documentation reference : 
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V01.00 NZR 05/20/2024
** - Baseline Created
**
*******************************************************************************/

/*************************** Inclusion files **********************************/
#include "System.h"
/********************** Component configuration *******************************/

/**************** Declaration of local symbol and constants *******************/

/******************** Declaration of local macros *****************************/

/********************* Declaration of local types *****************************/

/******************* Declaration of local variables ***************************/

/********************* Declaration of local constants *************************/

/******************** Declaration of exported variables ***********************/
EXTERN VAR(uint32, SYS_VAR) __vector_table;	/* Declaration of interrupt vector table */

/****************** Declaration of exported constant **************************/

/*******************************************************************************
** FUNCTIONS **
*******************************************************************************/
/******************** Internal functions declarations *************************/
STATIC FUNC(void, SYS_CODE) PMU_Init(void);
STATIC FUNC(void, SYS_CODE) InitAllModules(void);
/************************** Function definitions ******************************/
/*****************************************************************************
* Function: main1
* Description: main function
* Parameters: None
* Returns: None
* Note: None
****************************************************************************/
FUNC(void, SYS_CODE) main1(void)
{
	InitAllModules();
	/* Initialize Scheduler*/
	//RTSC_Start();
	/*Scheduler*/
	//RTSC_Scheduler();
	while(1){;}
}

/********************** Internal Function definitions *************************/

/*****************************************************************************
* Function: PMU_Init
* Description: Configure the Power Management Unit (PMU).
* Parameters:
* Returns:
* Note: 1. The Power Management Unit (PMU) controls the internal power supply of the chip, enables, monitors, 
		   and low-voltage monitors the MCU after standby, etc. Please refer to the reference manual.
*       2. Initialize the PMU module, which must be done before initializing other modules.
****************************************************************************/
STATIC FUNC(void, SYS_CODE) PMU_Init(void)
{	 
	/* After entering STANDBY, the IO ports will be isolated from the ADC, CMP, and SPLL modules. After exiting STANDBY, this isolation state must be removed. Refer to the PMU_ISO_CLR register. */
	PMU_IsoClr();	 /* Clear the isolation state flag between ports and functional modules, remove the isolation state between IO ports and ADC, CMP, and SPLL modules, must be done */

	/* Initialize PMU interrupt */
	PMU_IntMask(PMU_INT_ALL, MASK);		/* Disable PMU interrupt */

	/* Initialize variables */

	/* Start the module, the module starts running */
}

/*****************************************************************************
 * Function: InitAllModules
 * Description: Initialize the system and all MCU integrated peripherals used.
 * Parameters:
 * Returns:
 * Note:
****************************************************************************/
STATIC FUNC(void, SYS_CODE) InitAllModules(void)
{
	SCB->VTOR = ((uint32_t)&__vector_table) & 0xFFFFFF80U;	/* Clear the last 7 bits of the VTOR register */
	CORE_DSB();	/* Data synchronization, before this instruction, complete all storage processing work. */
	
	CORE_DISABLE_IRQ();	/* Disable global IRQ */
	
	PMU_Init();			/* Initialize the PMU module */
	CLKT_Init();		/* Initialize the system clock source */
	TIMT_Init();        /* Initialize system clock module */


	SYSCTRL_ResetModule(SYSCTRL_HWDIV);
	SYSCTRL_EnableModule(SYSCTRL_HWDIV);
	HWDIV_Init();
	CORE_ENABLE_IRQ();	/* Enable global IRQ */
}
