<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_EG9bAWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_EG9bAmtZEfCgw5R7bfzv1Q" bindingContexts="_EG9bC2tZEfCgw5R7bfzv1Q">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;Z20K116M_startup.S&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;System.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/System.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/System.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;cmsis_gcc.h&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/Platform/Core/cmsis_gcc.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/ZhiXinSDK/Platform/Core/cmsis_gcc.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;TIMT.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/TIMT.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/TIMT.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;System.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/System.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/System.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;RTSC.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/RTSC.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/RTSC.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;RTSC.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/RTSC.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/RTSC.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;PWMT.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/PWMT.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/PWMT.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;PWMT.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/PWMT.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/PWMT.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;PWMT_Cfg.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/PWMT_Cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/PWMT_Cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Compiler.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/Compiler.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/Compiler.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Compiler_Cfg.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/Compiler_Cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/Compiler_Cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;CLKT.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/CLKT.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/CLKT.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;CLKT.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/CLKT.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/CLKT.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;TIMT.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/TIMT.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/TIMT.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;OS.c&quot; tooltip=&quot;48V code/03_BSW/System/01_Service/OS.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/01_Service/OS.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;48V code/03_BSW/STAR/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/STAR/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;OS.h&quot; tooltip=&quot;48V code/03_BSW/System/01_Service/OS.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/01_Service/OS.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;EcuM.h&quot; tooltip=&quot;48V code/03_BSW/System/01_Service/EcuM.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/01_Service/EcuM.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Wdgif.h&quot; tooltip=&quot;48V code/03_BSW/System/02_HAL/Wdgif.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/02_HAL/Wdgif.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Gpt.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/Gpt.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/Gpt.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Z20K11xM_stim.c&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;NvM.c&quot; tooltip=&quot;48V code/03_BSW/Memory/01_Service/NvM.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/Memory/01_Service/NvM.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform_cfg.h&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/MCU/platform_cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/ZhiXinSDK/MCU/platform_cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;NvM.c&quot; tooltip=&quot;48V code/03-BSW/Memory/NvM.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/NvM.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;MemIf.c&quot; tooltip=&quot;48V code/03-BSW/Memory/MemIf.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/MemIf.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Fee.c&quot; tooltip=&quot;48V code/03-BSW/Memory/Fee.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/Fee.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;MemIf.h&quot; tooltip=&quot;48V code/03-BSW/Memory/MemIf.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/MemIf.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Fee.h&quot; tooltip=&quot;48V code/03-BSW/Memory/Fee.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/Fee.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Fls.c&quot; tooltip=&quot;48V code/03-BSW/Memory/Fls.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/Fls.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;OS.c&quot; tooltip=&quot;48V code/03-BSW/System/OS.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/OS.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;OS.h&quot; tooltip=&quot;48V code/03-BSW/System/OS.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/OS.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;NvM.h&quot; tooltip=&quot;48V code/03-BSW/Memory/NvM.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/NvM.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;48V code/07-source/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/07-source/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Wdgm.c&quot; tooltip=&quot;48V code/03-BSW/System/Wdgm.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/Wdgm.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Pwm.c&quot; tooltip=&quot;48V code/03-BSW/IO/Pwm.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/Pwm.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Mcu.c&quot; tooltip=&quot;48V code/03-BSW/System/Mcu.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/Mcu.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;AdcIf.c&quot; tooltip=&quot;48V code/03-BSW/IO/AdcIf.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/AdcIf.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;AdcIf.h&quot; tooltip=&quot;48V code/03-BSW/IO/AdcIf.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/AdcIf.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Adc.c&quot; tooltip=&quot;48V code/03-BSW/IO/Adc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/Adc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Dio.c&quot; tooltip=&quot;48V code/03-BSW/IO/Dio.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/Dio.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;core_cm0plus.h&quot; tooltip=&quot;48V code/ZhixinSDK/Platform/Core/core_cm0plus.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/ZhixinSDK/Platform/Core/core_cm0plus.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Gpt.c&quot; tooltip=&quot;48V code/03-BSW/System/Gpt.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/Gpt.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform_cfg.h&quot; tooltip=&quot;48V code/ZhixinSDK/Platform/platform_cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/ZhixinSDK/Platform/platform_cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;core_cm0plus.h&quot; tooltip=&quot;48V code/ZhixinSDK/CORE/core_cm0plus.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/ZhixinSDK/CORE/core_cm0plus.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Z20K11xM_adc.c&quot; tooltip=&quot;48V code/ZhixinSDK/StdDriver/Src/Z20K11xM_adc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/ZhixinSDK/StdDriver/Src/Z20K11xM_adc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;HeatingControl.h&quot; tooltip=&quot;48V code/01-SWC/HMC/HeatingControl.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/01-SWC/HMC/HeatingControl.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;arm_compat.h&quot; tooltip=&quot;48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/0_Core/arm_compat.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/0_Core/arm_compat.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform_cfg.h&quot; tooltip=&quot;48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/1_MCU/platform_cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/1_MCU/platform_cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;FastIRQ.c&quot; tooltip=&quot;48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/7_Source/FastIRQ.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/7_Source/FastIRQ.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;core_cm0plus.h&quot; tooltip=&quot;48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/0_Core/core_cm0plus.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/0_Core/core_cm0plus.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_EG9bAmtZEfCgw5R7bfzv1Q" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_EXcuE2tZEfCgw5R7bfzv1Q" label="%trimmedwindow.label.eclipseSDK" x="0" y="0" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1753669076430"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_EXcuE2tZEfCgw5R7bfzv1Q" selectedElement="_EXcuFGtZEfCgw5R7bfzv1Q" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_EXcuFGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_LxUIQHNUEfCVj4cwJJjSAw">
        <children xsi:type="advanced:Perspective" xmi:id="_Ehfa5GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_Ehfa5WtZEfCgw5R7bfzv1Q" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.autotools.ui.wizards.conversionWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.profileActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.perspSC:org.eclipse.embedcdt.internal.codered.ui.perspectives.CodeRedPerspective</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_Ehfa5WtZEfCgw5R7bfzv1Q" selectedElement="_Ehfa6mtZEfCgw5R7bfzv1Q" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_Ehfa5mtZEfCgw5R7bfzv1Q" elementId="topLeft" containerData="1609" selectedElement="_Ehfa52tZEfCgw5R7bfzv1Q">
              <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa52tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_EhWRLGtZEfCgw5R7bfzv1Q" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa6GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_EhWRLWtZEfCgw5R7bfzv1Q" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa6WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_EhWRLmtZEfCgw5R7bfzv1Q" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_Ehfa6mtZEfCgw5R7bfzv1Q" containerData="8391" selectedElement="_Ehfa82tZEfCgw5R7bfzv1Q">
              <children xsi:type="basic:PartSashContainer" xmi:id="_Ehfa62tZEfCgw5R7bfzv1Q" containerData="7500" selectedElement="_Ehfa7GtZEfCgw5R7bfzv1Q" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa7GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_EhMf8GtZEfCgw5R7bfzv1Q"/>
                <children xsi:type="basic:PartStack" xmi:id="_Ehfa7WtZEfCgw5R7bfzv1Q" elementId="topRight" containerData="2500" selectedElement="_Ehfa7mtZEfCgw5R7bfzv1Q">
                  <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa7mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ContentOutline" ref="_EhWRM2tZEfCgw5R7bfzv1Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa72tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_EhWRNGtZEfCgw5R7bfzv1Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_Ehfa4GtZEfCgw5R7bfzv1Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Mylyn</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa8WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_Ehfa4mtZEfCgw5R7bfzv1Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa8mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" ref="_Ehfa42tZEfCgw5R7bfzv1Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:CMSIS Packs</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_Ehfa82tZEfCgw5R7bfzv1Q" elementId="bottom" containerData="2500" selectedElement="_Ehfa9mtZEfCgw5R7bfzv1Q">
                <tags>Oomph</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa9GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ProblemView" ref="_EhWRL2tZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa9WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.TaskList" ref="_EhWRMGtZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa9mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.console.ConsoleView" ref="_EhWRMWtZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa92tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.PropertySheet" ref="_EhWRMmtZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Ehfa-GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_Ehfa4WtZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_LxUIQHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_LxUvUHNUEfCVj4cwJJjSAw" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.svg">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.xml.ui.perspective</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser</tags>
          <tags>persp.perspSC:org.eclipse.embedcdt.internal.codered.ui.perspectives.CodeRedPerspective</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.debuggerConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.debugsources.view</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.svg</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_LxUvUHNUEfCVj4cwJJjSAw" selectedElement="_LxUvV3NUEfCVj4cwJJjSAw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_LxUvUXNUEfCVj4cwJJjSAw" containerData="1560" selectedElement="_LxUvUnNUEfCVj4cwJJjSAw" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_LxUvUnNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_LxUvVHNUEfCVj4cwJJjSAw">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvU3NUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.DebugView" ref="_LxAmQHNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvVHNUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_EhWRLGtZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                  <tags>active</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_LxUvVXNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvVnNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_LxQd4XNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_LxUvV3NUEfCVj4cwJJjSAw" containerData="8440" selectedElement="_LxUvWHNUEfCVj4cwJJjSAw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_LxUvWHNUEfCVj4cwJJjSAw" containerData="7500" selectedElement="_LxUvWXNUEfCVj4cwJJjSAw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvWXNUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.editorss" containerData="6500" ref="_EhMf8GtZEfCgw5R7bfzv1Q"/>
                <children xsi:type="basic:PartStack" xmi:id="_LxUvWnNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="3500" selectedElement="_LxUvXXNUEfCVj4cwJJjSAw">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvW3NUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.VariableView" ref="_LxDCgnNUEfCVj4cwJJjSAw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvXHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.BreakpointView" ref="_LxDpkHNUEfCVj4cwJJjSAw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvXXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.ExpressionView" ref="_LxEQoHNUEfCVj4cwJJjSAw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvXnNUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_EhWRM2tZEfCgw5R7bfzv1Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvX3NUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_EhWRMmtZEfCgw5R7bfzv1Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvYHNUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_EhWRNGtZEfCgw5R7bfzv1Q" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvYXNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_LxOosHNUEfCVj4cwJJjSAw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvYnNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_LxOosXNUEfCVj4cwJJjSAw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvY3NUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" toBeRendered="false" ref="_LxP20HNUEfCVj4cwJJjSAw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_LxUvZHNUEfCVj4cwJJjSAw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" ref="_LxSTEHNUEfCVj4cwJJjSAw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_LxUvZXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="2500" selectedElement="_LxUvZnNUEfCVj4cwJJjSAw">
                <tags>Debug</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvZnNUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.console.ConsoleView" ref="_EhWRMWtZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvZ3NUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.RegisterView" ref="_LxDCgHNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvaHNUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_EhWRLmtZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvaXNUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_LxDCgXNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvanNUEfCVj4cwJJjSAw" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_LxJJIHNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUva3NUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.views.ProblemView" ref="_EhWRL2tZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvbHNUEfCVj4cwJJjSAw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_Ehfa4WtZEfCgw5R7bfzv1Q" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvbXNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" toBeRendered="false" ref="_LxQd4HNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvbnNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" ref="_LxQd4nNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_LxUvb3NUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" toBeRendered="false" ref="_LxRE8HNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_ML_SEXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.MemoryView" ref="_ML_SEHNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_MRD_UXNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_MRD_UHNUEfCVj4cwJJjSAw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_EXcuFWtZEfCgw5R7bfzv1Q" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_EXcuFmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_EXcuEGtZEfCgw5R7bfzv1Q" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_EXcuF2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_EXcuEWtZEfCgw5R7bfzv1Q" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_EXcuGGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_EXcuEmtZEfCgw5R7bfzv1Q" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_EXcuEGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EXcuEWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_EywKAGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_EywKAWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EXcuEmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_EhMf8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.editorss" selectedElement="_EhMf8WtZEfCgw5R7bfzv1Q">
      <children xsi:type="basic:PartStack" xmi:id="_EhMf8WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_0ukX0HNzEfCIHPp1rUlC6w">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <children xsi:type="basic:Part" xmi:id="_0ukX0HNzEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Z20K116M_startup.S" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/s_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;Z20K116M_startup.S&quot; partName=&quot;Z20K116M_startup.S&quot; title=&quot;Z20K116M_startup.S&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/48V code/03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;5478&quot; selectionTopPixel=&quot;1860&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.asm.AsmEditor</tags>
          <tags>active</tags>
          <tags>activeOnClose</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRLGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Aggregate for window 1753669076430&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Eoc-gGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Eoc-gWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRLWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRLmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRL2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_EvuEIGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_EvuEIWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRMGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.priorityField=&quot;30&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_jT7vYGuQEfCKD-eSyML_Aw" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_jT7vYWuQEfCKD-eSyML_Aw" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRMWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WlgZ8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WlgZ8WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.console.ConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRMmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRM2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_EuiYYGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_EuiYYWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_EhWRNGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Ehfa4GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Ehfa4WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Ehfa4mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Ehfa42tZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
      <tags>View</tags>
      <tags>categoryTag:CMSIS Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxAmQHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_Lyf0AHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Lyf0AXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxDCgHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_MPjjcHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_MPjjcXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxDCgXNUEfCVj4cwJJjSAw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxDCgnNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_L4S6AHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_L4S6AXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxDpkHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_MIzbMHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_MIzbMXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxEQoHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_MGmbwHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_MGmbwXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxJJIHNUEfCVj4cwJJjSAw" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxOosHNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxOosXNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxP20HNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxQd4HNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxQd4XNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxQd4nNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_MQk3IHNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_MQk3IXNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxRE8HNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_LxSTEHNUEfCVj4cwJJjSAw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_MSlpUHNUEfCVj4cwJJjSAw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_MSlpUXNUEfCVj4cwJJjSAw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_ML_SEHNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_MMQX0HNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_MMQ-4HNUEfCVj4cwJJjSAw" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_MRD_UHNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_MRNwUHNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.executablesView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_MRNwUXNUEfCVj4cwJJjSAw" elementId="org.eclipse.cdt.debug.ui.executablesView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_EG9bA2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xAGtZEfCgw5R7bfzv1Q" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_EY9xAWtZEfCgw5R7bfzv1Q" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xAmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_DwRHV3NyEfCIHPp1rUlC6w" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.svg" tooltip="Print" command="_EHoyNGtZEfCgw5R7bfzv1Q"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xA2tZEfCgw5R7bfzv1Q" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_EY9xBGtZEfCgw5R7bfzv1Q" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xBWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_DwRHXnNyEfCIHPp1rUlC6w" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.svg" tooltip="Undo" enabled="false" command="_EHfAyWtZEfCgw5R7bfzv1Q"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_DwRHX3NyEfCIHPp1rUlC6w" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.svg" tooltip="Redo" enabled="false" command="_EHow62tZEfCgw5R7bfzv1Q"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xBmtZEfCgw5R7bfzv1Q" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_EY9xB2tZEfCgw5R7bfzv1Q" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EirtsGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EjfmAGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EjGkcGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Eh7fwGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.actionSet.presentation" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xCGtZEfCgw5R7bfzv1Q" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_EY9xCWtZEfCgw5R7bfzv1Q" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xCmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_DwRHZXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.svg" tooltip="Pin Editor" type="Check" command="_EHox4mtZEfCgw5R7bfzv1Q"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xC2tZEfCgw5R7bfzv1Q" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_EY9xDGtZEfCgw5R7bfzv1Q" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xDWtZEfCgw5R7bfzv1Q" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_EY9xDmtZEfCgw5R7bfzv1Q" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_EY9xD2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_EcBsEGtZEfCgw5R7bfzv1Q" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_EcBsEWtZEfCgw5R7bfzv1Q" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_EG9bBGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_EG9bBWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_EG9bBmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_EG9bB2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_EG9bCGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_E1J9wGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Io_w0HNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_EG9bCWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_Iph8UHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.OutlineFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Ip9aIHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <bindingTables xmi:id="_EG9bCmtZEfCgw5R7bfzv1Q" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_EG9bC2tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rcWtZEfCgw5R7bfzv1Q" keySequence="CTRL+1" command="_EHfAhmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rkWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+L" command="_EHoya2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r02tZEfCgw5R7bfzv1Q" keySequence="CTRL+V" command="_EHe_4WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r8WtZEfCgw5R7bfzv1Q" keySequence="CTRL+A" command="_EHowymtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r_mtZEfCgw5R7bfzv1Q" keySequence="CTRL+C" command="_EHoxIGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sHmtZEfCgw5R7bfzv1Q" keySequence="CTRL+X" command="_EHowdGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sH2tZEfCgw5R7bfzv1Q" keySequence="CTRL+Y" command="_EHow62tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sImtZEfCgw5R7bfzv1Q" keySequence="CTRL+Z" command="_EHfAyWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sPWtZEfCgw5R7bfzv1Q" keySequence="ALT+PAGE_UP" command="_EHow9WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sPmtZEfCgw5R7bfzv1Q" keySequence="ALT+PAGE_DOWN" command="_EHoxrWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sQmtZEfCgw5R7bfzv1Q" keySequence="SHIFT+INSERT" command="_EHe_4WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sR2tZEfCgw5R7bfzv1Q" keySequence="ALT+F11" command="_EHfAImtZEfCgw5R7bfzv1Q">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_EH7si2tZEfCgw5R7bfzv1Q" keySequence="CTRL+F10" command="_EHfABWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7slWtZEfCgw5R7bfzv1Q" keySequence="CTRL+INSERT" command="_EHoxIGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7somtZEfCgw5R7bfzv1Q" keySequence="CTRL+PAGE_UP" command="_EHoySWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7so2tZEfCgw5R7bfzv1Q" keySequence="CTRL+PAGE_DOWN" command="_EHfAjmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7spmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+F1" command="_EHfANmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sp2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+F2" command="_EHoxlWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sqGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+F3" command="_EHoyPmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ssWtZEfCgw5R7bfzv1Q" keySequence="SHIFT+DEL" command="_EHowdGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s2mtZEfCgw5R7bfzv1Q" keySequence="ALT+/" command="_EHoyCGtZEfCgw5R7bfzv1Q">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_EHyhcGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.textEditorScope" bindingContext="_EHoy6WtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EHyhcWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+CR" command="_EHoyPWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rYmtZEfCgw5R7bfzv1Q" keySequence="CTRL+BS" command="_EHe_tmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rcGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+Q" command="_EHfAbWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rdWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+C" command="_EHe_92tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ri2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+J" command="_EHfAXmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rjWtZEfCgw5R7bfzv1Q" keySequence="CTRL++" command="_EHoxjGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rlmtZEfCgw5R7bfzv1Q" keySequence="CTRL+-" command="_EHowsWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7romtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+P" command="_EHox0WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rrWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+C" command="_EHe_wmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rsWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+V" command="_EHfAEmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rs2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F" command="_EHfAsWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rwWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+J" command="_EHfAfGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rymtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+A" command="_EHoxP2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r0mtZEfCgw5R7bfzv1Q" keySequence="CTRL+T" command="_EHoypmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r2WtZEfCgw5R7bfzv1Q" keySequence="CTRL+J" command="_EHfADGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r3WtZEfCgw5R7bfzv1Q" keySequence="CTRL+L" command="_EHoyIWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r6GtZEfCgw5R7bfzv1Q" keySequence="CTRL+O" command="_EHoyiWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r7mtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+/" command="_EHfAu2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sBmtZEfCgw5R7bfzv1Q" keySequence="CTRL+D" command="_EHfAFmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sF2tZEfCgw5R7bfzv1Q" keySequence="CTRL+=" command="_EHoxjGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sGmtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+/" command="_EHoyfWtZEfCgw5R7bfzv1Q">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_EH7sHGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Y" command="_EHe_q2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sJWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+DEL" command="_EHoyDWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sJmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+X" command="_EHoxKWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sJ2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+Y" command="_EHowr2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sK2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+\" command="_EHoxVWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sLWtZEfCgw5R7bfzv1Q" keySequence="CTRL+DEL" command="_EHfAxGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sM2tZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_UP" command="_EHoyz2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sNmtZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_DOWN" command="_EHoxuWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sP2tZEfCgw5R7bfzv1Q" keySequence="SHIFT+END" command="_EHowt2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sSGtZEfCgw5R7bfzv1Q" keySequence="SHIFT+HOME" command="_EHownWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sTmtZEfCgw5R7bfzv1Q" keySequence="END" command="_EHoyVmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sUGtZEfCgw5R7bfzv1Q" keySequence="INSERT" command="_EHoxY2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sV2tZEfCgw5R7bfzv1Q" keySequence="F2" command="_EHfAkGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7saGtZEfCgw5R7bfzv1Q" keySequence="HOME" command="_EHoyeGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sa2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+ARROW_UP" command="_EHoyqGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sbWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+ARROW_DOWN" command="_EHow0GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7scWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+INSERT" command="_EHfAR2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7seGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_EHowuWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7seWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_EHfAT2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sjGtZEfCgw5R7bfzv1Q" keySequence="CTRL+F10" command="_EHoyOGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sk2tZEfCgw5R7bfzv1Q" keySequence="CTRL+END" command="_EHoxu2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7snWtZEfCgw5R7bfzv1Q" keySequence="CTRL+ARROW_UP" command="_EHfANGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7snmtZEfCgw5R7bfzv1Q" keySequence="CTRL+ARROW_DOWN" command="_EHoy3mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7soGtZEfCgw5R7bfzv1Q" keySequence="CTRL+ARROW_LEFT" command="_EHoxGmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7soWtZEfCgw5R7bfzv1Q" keySequence="CTRL+ARROW_RIGHT" command="_EHfAamtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7spGtZEfCgw5R7bfzv1Q" keySequence="CTRL+HOME" command="_EHe_4GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sqWtZEfCgw5R7bfzv1Q" keySequence="CTRL+NUMPAD_MULTIPLY" command="_EHox0GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sqmtZEfCgw5R7bfzv1Q" keySequence="CTRL+NUMPAD_ADD" command="_EHoylmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7srWtZEfCgw5R7bfzv1Q" keySequence="CTRL+NUMPAD_SUBTRACT" command="_EHoyO2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7srmtZEfCgw5R7bfzv1Q" keySequence="CTRL+NUMPAD_DIVIDE" command="_EHfAOGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7suGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_EHox2WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sumtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_EHoxZGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s3GtZEfCgw5R7bfzv1Q" keySequence="SHIFT+CR" command="_EHoydWtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rYGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_EHozE2tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rYWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+SHIFT+C" command="_EHoy4WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rY2tZEfCgw5R7bfzv1Q" keySequence="CTRL+TAB" command="_EHoy2mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7raWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+P" command="_EHowkmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7remtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+T" command="_EHfAomtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rgmtZEfCgw5R7bfzv1Q" keySequence="CTRL+7" command="_EHe_5GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rh2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+H" command="_EHoxd2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rl2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+N" command="_EHfAaWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rm2tZEfCgw5R7bfzv1Q" keySequence="CTRL+/" command="_EHe_5GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rnGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+O" command="_EHowhmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rpGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+A" command="_EHoyP2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rp2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+S" command="_EHoyvGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rqGtZEfCgw5R7bfzv1Q" keySequence="CTRL+#" command="_EHox72tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rqmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+C" command="_EHe_5GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rsmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F" command="_EHoy22tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rtWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+G" command="_EHe_r2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ruGtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+H" command="_EHfAiWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ru2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+I" command="_EHoxTGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r0GtZEfCgw5R7bfzv1Q" keySequence="CTRL+T" command="_EHoxvmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r12tZEfCgw5R7bfzv1Q" keySequence="CTRL+I" command="_EHfAW2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r5GtZEfCgw5R7bfzv1Q" keySequence="CTRL+O" command="_EHoxMWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r7WtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+/" command="_EHoyk2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r8mtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+R" command="_EHoyFGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r_2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+S" command="_EHfAB2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sAWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+T" command="_EHox9WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sDWtZEfCgw5R7bfzv1Q" keySequence="CTRL+G" command="_EHoytGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sFGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+L" command="_EHow0mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sFWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+M" command="_EHe_12tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sFmtZEfCgw5R7bfzv1Q" keySequence="CTRL+=" command="_EHox72tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sGWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+O" command="_EHowemtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sHWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Z" command="_EHoyLGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sKmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+\" command="_EHoxSGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sWWtZEfCgw5R7bfzv1Q" keySequence="F3" command="_EHoy5mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sYGtZEfCgw5R7bfzv1Q" keySequence="F4" command="_EHoyjmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sdGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+ARROW_UP" command="_EHoyGmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sdmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_EHoxsGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7se2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_UP" command="_EHowo2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sgmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_DOWN" command="_EHoy3GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7siGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_LEFT" command="_EHoyCWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sjmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_EHoyZmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7swGtZEfCgw5R7bfzv1Q" keySequence="ALT+C" command="_EHoxSWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s22tZEfCgw5R7bfzv1Q" keySequence="SHIFT+TAB" command="_EHoxeGtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rZGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_EHozDGtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rZWtZEfCgw5R7bfzv1Q" keySequence="CTRL+CR" command="_EHfAtGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rzWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+C" command="_EHoxCGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r9WtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+R" command="_EHowuGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sB2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+U" command="_EHoxx2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sEmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+I" command="_EHowsmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sMWtZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_UP" command="_EHoxm2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sNWtZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_DOWN" command="_EHfAuWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sQGtZEfCgw5R7bfzv1Q" keySequence="SHIFT+INSERT" command="_EHfAKGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sT2tZEfCgw5R7bfzv1Q" keySequence="INSERT" command="_EHowrWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sY2tZEfCgw5R7bfzv1Q" keySequence="F4" command="_EHfAAWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sfWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_UP" command="_EHox92tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7shGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_DOWN" command="_EHowtGtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rZmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.contexts.window" bindingContext="_EG9bDGtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rZ2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+SHIFT+T" command="_EHfABGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7raGtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+SHIFT+L" command="_EHoxVGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rbmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q O" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rb2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_EH7rcmtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+B" command="_EHoxp2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rc2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+R" command="_EHoy42tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rdGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q Q" command="_EHoxnGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7reGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+S" command="_EHoxhWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7reWtZEfCgw5R7bfzv1Q" keySequence="CTRL+3" command="_EHfAj2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rfWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+T" command="_EHowfmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rfmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q S" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rf2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_EH7rg2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q V" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rhGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_EH7rhWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+G" command="_EHoxk2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rhmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+W" command="_EHowc2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7riWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q H" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rimtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_EH7rjGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+K" command="_EHfAMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rjmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q K" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rj2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_EH7rkGtZEfCgw5R7bfzv1Q" keySequence="CTRL+," command="_EHe_5WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rlGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q L" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rlWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_EH7rmGtZEfCgw5R7bfzv1Q" keySequence="CTRL+." command="_EHoyq2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7roWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+P" command="_EHownmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rpmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+B" command="_EHfAMWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rqWtZEfCgw5R7bfzv1Q" keySequence="CTRL+#" command="_EHfABmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rr2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+T" command="_EHoxHGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rsGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+E" command="_EHfAQ2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rvWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q X" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rvmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_EH7rv2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q Y" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rwGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_EH7rwmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q Z" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7rw2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_EH7ryGtZEfCgw5R7bfzv1Q" keySequence="CTRL+P" command="_EHoyNGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ryWtZEfCgw5R7bfzv1Q" keySequence="CTRL+Q" command="_EHoyRGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rz2tZEfCgw5R7bfzv1Q" keySequence="CTRL+S" command="_EHows2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r1WtZEfCgw5R7bfzv1Q" keySequence="CTRL+W" command="_EHow8WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r1mtZEfCgw5R7bfzv1Q" keySequence="CTRL+H" command="_EHoyB2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r2mtZEfCgw5R7bfzv1Q" keySequence="CTRL+K" command="_EHoxqmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r3mtZEfCgw5R7bfzv1Q" keySequence="CTRL+M" command="_EHoyAmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r4WtZEfCgw5R7bfzv1Q" keySequence="CTRL+N" command="_EHoyvmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r8GtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+?" command="_EHfAo2tZEfCgw5R7bfzv1Q">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_EH7r92tZEfCgw5R7bfzv1Q" keySequence="CTRL+B" command="_EHe_6mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r-GtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q B" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7r-WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_EH7sAmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+Q C" command="_EHoxnGtZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7sA2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_EH7sCWtZEfCgw5R7bfzv1Q" keySequence="CTRL+E" command="_EHfAv2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sCmtZEfCgw5R7bfzv1Q" keySequence="CTRL+F" command="_EHfAHWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sEGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+W" command="_EHoyp2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sEWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+H" command="_EHfAtmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sGGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+N" command="_EHowcGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sI2tZEfCgw5R7bfzv1Q" keySequence="CTRL+_" command="_EHfAp2tZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7sJGtZEfCgw5R7bfzv1Q" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_EH7sKGtZEfCgw5R7bfzv1Q" keySequence="CTRL+{" command="_EHfAp2tZEfCgw5R7bfzv1Q">
      <parameters xmi:id="_EH7sKWtZEfCgw5R7bfzv1Q" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_EH7sNGtZEfCgw5R7bfzv1Q" keySequence="SHIFT+F9" command="_EHowhWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sOWtZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_LEFT" command="_EHfACWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sPGtZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_RIGHT" command="_EHowiGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sRGtZEfCgw5R7bfzv1Q" keySequence="SHIFT+F5" command="_EHow2GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sRmtZEfCgw5R7bfzv1Q" keySequence="ALT+F7" command="_EHoxR2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sS2tZEfCgw5R7bfzv1Q" keySequence="F9" command="_EHowlWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sTGtZEfCgw5R7bfzv1Q" keySequence="F11" command="_EHoyh2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sTWtZEfCgw5R7bfzv1Q" keySequence="F12" command="_EHoyC2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sVmtZEfCgw5R7bfzv1Q" keySequence="F2" command="_EHe_6WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sZWtZEfCgw5R7bfzv1Q" keySequence="F5" command="_EHowj2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7saWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F7" command="_EHoyi2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7samtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F8" command="_EHfApmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sbGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F9" command="_EHowwmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sbmtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+ARROW_LEFT" command="_EHoyRGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sb2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+ARROW_RIGHT" command="_EHfAO2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7scGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F12" command="_EHe_ymtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7scmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F4" command="_EHowc2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sc2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F6" command="_EHoxi2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7semtZEfCgw5R7bfzv1Q" keySequence="CTRL+F7" command="_EHoxIWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sgWtZEfCgw5R7bfzv1Q" keySequence="CTRL+F8" command="_EHfAh2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sh2tZEfCgw5R7bfzv1Q" keySequence="CTRL+F9" command="_EHfARmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sjWtZEfCgw5R7bfzv1Q" keySequence="CTRL+F11" command="_EHoyWWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7skWtZEfCgw5R7bfzv1Q" keySequence="CTRL+F12" command="_EHfAM2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7smWtZEfCgw5R7bfzv1Q" keySequence="CTRL+F4" command="_EHow8WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sm2tZEfCgw5R7bfzv1Q" keySequence="CTRL+F6" command="_EHfAJmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7snGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+F7" command="_EHoxvWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ssmtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_EHoxcmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ss2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_EHoyy2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7stGtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_EHoxX2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7st2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_EHoxhGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7suWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_EHfAqmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7svGtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+SHIFT+F12" command="_EHoymGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7svWtZEfCgw5R7bfzv1Q" keySequence="DEL" command="_EHfAK2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7syWtZEfCgw5R7bfzv1Q" keySequence="ALT+?" command="_EHfAo2tZEfCgw5R7bfzv1Q">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_EH7s2WtZEfCgw5R7bfzv1Q" keySequence="ALT+-" command="_EHoxNGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s3WtZEfCgw5R7bfzv1Q" keySequence="ALT+CR" command="_EHox8mtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7ramtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_EHozBGtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7ra2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+P" command="_EHowqmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rt2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+G" command="_EHoyVWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rumtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+H" command="_EHoyEmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r9GtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+R" command="_EHe_6WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sXmtZEfCgw5R7bfzv1Q" keySequence="F3" command="_EHoyRWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sYmtZEfCgw5R7bfzv1Q" keySequence="F4" command="_EHe_smtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sfGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_UP" command="_EHoxQ2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sg2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_DOWN" command="_EHoxRWtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rbGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_EHoy7mtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rbWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+P" command="_EHoxQGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rpWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+A" command="_EHoy32tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rrmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+C" command="_EHoycGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rtGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+F" command="_EHoyumtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rx2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+>" command="_EHoyI2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r2GtZEfCgw5R7bfzv1Q" keySequence="CTRL+I" command="_EHoyNmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r7GtZEfCgw5R7bfzv1Q" keySequence="CTRL+O" command="_EHoxTmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r72tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+/" command="_EHoxM2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sLGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+\" command="_EHoxlGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sX2tZEfCgw5R7bfzv1Q" keySequence="F3" command="_EHoxPWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sdWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+ARROW_UP" command="_EHfAWmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sd2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_EHow4GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sgGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_UP" command="_EHoxtGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7shmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_DOWN" command="_EHoxy2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7simtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_LEFT" command="_EHe_-mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7skGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_EHoxeWtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rdmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.compareEditorScope" bindingContext="_EHoy62tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rd2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+C" command="_EHe_92tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ro2tZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+P" command="_EHox0WtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7re2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_EHoy-mtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rfGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+T" command="_EHfAomtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7riGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+H" command="_EHoxd2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rtmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+G" command="_EHe_r2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7ruWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+H" command="_EHfAiWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rvGtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+I" command="_EHoxTGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r82tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+R" command="_EHoyFGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sDmtZEfCgw5R7bfzv1Q" keySequence="CTRL+G" command="_EHoytGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sXGtZEfCgw5R7bfzv1Q" keySequence="F3" command="_EHoy5mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sYWtZEfCgw5R7bfzv1Q" keySequence="F4" command="_EHoyjmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rgGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_EHoy52tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rgWtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+V" command="_EHowqGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rrGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+C" command="_EHox6GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sMmtZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_UP" command="_EHe_tGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sO2tZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_RIGHT" command="_EHoyhWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sQWtZEfCgw5R7bfzv1Q" keySequence="SHIFT+INSERT" command="_EHowqGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7slGtZEfCgw5R7bfzv1Q" keySequence="CTRL+INSERT" command="_EHox6GtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rkmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_EHozDWtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rk2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+M" command="_EHe_1GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rzmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+C" command="_EHoxCGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r6WtZEfCgw5R7bfzv1Q" keySequence="CTRL+O" command="_EHoyn2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r9mtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+R" command="_EHowuGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sAGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+S" command="_EHoweWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sCGtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+U" command="_EHoxx2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sE2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+I" command="_EHowsmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rmWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_EHozDmtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rmmtZEfCgw5R7bfzv1Q" keySequence="CTRL+/" command="_EHoxomtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sWGtZEfCgw5R7bfzv1Q" keySequence="F3" command="_EHoxfmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rnWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.rpm.ui.specEditorScope" bindingContext="_EHozC2tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rnmtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+O" command="_EHoyDmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rq2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+C" command="_EHfAAGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r52tZEfCgw5R7bfzv1Q" keySequence="CTRL+O" command="_EHfASmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sG2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+R D" command="_EHoydGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7su2tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+R P" command="_EHoxUWtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rn2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_EHozEmtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7roGtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+O" command="_EHe_22tZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7rxGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_EHoy7WtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rxWtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+M" command="_EHowxmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7rxmtZEfCgw5R7bfzv1Q" keySequence="ALT+CTRL+N" command="_EHoymWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r0WtZEfCgw5R7bfzv1Q" keySequence="CTRL+T" command="_EHfAk2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r1GtZEfCgw5R7bfzv1Q" keySequence="CTRL+W" command="_EHoxamtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r4GtZEfCgw5R7bfzv1Q" keySequence="CTRL+N" command="_EHoxj2tZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7ry2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.debugging" bindingContext="_EHozBWtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7rzGtZEfCgw5R7bfzv1Q" keySequence="CTRL+R" command="_EHoxJmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sSWtZEfCgw5R7bfzv1Q" keySequence="F7" command="_EHoysGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sSmtZEfCgw5R7bfzv1Q" keySequence="F8" command="_EHoxXWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sZGtZEfCgw5R7bfzv1Q" keySequence="F5" command="_EHe_9mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sZmtZEfCgw5R7bfzv1Q" keySequence="F6" command="_EHowumtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7smGtZEfCgw5R7bfzv1Q" keySequence="CTRL+F2" command="_EHoyD2tZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7r22tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_EHozBmtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7r3GtZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+," command="_EHoyR2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7r32tZEfCgw5R7bfzv1Q" keySequence="CTRL+SHIFT+." command="_EHox_2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sD2tZEfCgw5R7bfzv1Q" keySequence="CTRL+G" command="_EHoyAGtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7r4mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.editor.scope" bindingContext="_EHoy-2tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7r42tZEfCgw5R7bfzv1Q" keySequence="CTRL+O" command="_EHoxh2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sU2tZEfCgw5R7bfzv1Q" keySequence="F2" command="_EHfAwmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7r5WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_EHoy6mtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7r5mtZEfCgw5R7bfzv1Q" keySequence="CTRL+O" command="_EHow3GtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7r6mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_EHozEGtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7r62tZEfCgw5R7bfzv1Q" keySequence="CTRL+O" command="_EHe_22tZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7r-mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_EHozFWtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7r-2tZEfCgw5R7bfzv1Q" keySequence="CTRL+C" command="_EHfAmGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sn2tZEfCgw5R7bfzv1Q" keySequence="CTRL+ARROW_LEFT" command="_EHfAEWtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7r_GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_EHozCWtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7r_WtZEfCgw5R7bfzv1Q" keySequence="CTRL+C" command="_EHfAJ2tZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sBGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" bindingContext="_EHoy92tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sBWtZEfCgw5R7bfzv1Q" keySequence="CTRL+D" command="_EHoxQmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sC2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_EHozB2tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sDGtZEfCgw5R7bfzv1Q" keySequence="CTRL+G" command="_EHoyu2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sZ2tZEfCgw5R7bfzv1Q" keySequence="HOME" command="_EHfAkmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sIGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.console" bindingContext="_EHoy_WtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sIWtZEfCgw5R7bfzv1Q" keySequence="CTRL+Z" command="_EHoyomtZEfCgw5R7bfzv1Q">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_EH7sLmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_EHozCGtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sL2tZEfCgw5R7bfzv1Q" keySequence="SHIFT+F7" command="_EHoxfWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sMGtZEfCgw5R7bfzv1Q" keySequence="SHIFT+F8" command="_EHox_WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sQ2tZEfCgw5R7bfzv1Q" keySequence="SHIFT+F5" command="_EHowu2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sRWtZEfCgw5R7bfzv1Q" keySequence="SHIFT+F6" command="_EHe_7WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7smmtZEfCgw5R7bfzv1Q" keySequence="CTRL+F5" command="_EHoyf2tZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sN2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_EHozAWtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sOGtZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_LEFT" command="_EHoxv2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sOmtZEfCgw5R7bfzv1Q" keySequence="ALT+ARROW_RIGHT" command="_EHox1WtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sXWtZEfCgw5R7bfzv1Q" keySequence="F3" command="_EHoy5mtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sUWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_EHozD2tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sUmtZEfCgw5R7bfzv1Q" keySequence="F1" command="_EHe_vmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sVGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_EHozFmtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sVWtZEfCgw5R7bfzv1Q" keySequence="F2" command="_EHfALmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sWmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_EHozA2tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sW2tZEfCgw5R7bfzv1Q" keySequence="F3" command="_EHoy5mtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sfmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_EHoy_2tZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sf2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_UP" command="_EHowiWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7shWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_DOWN" command="_EHe_0mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7siWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_LEFT" command="_EHoxC2tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sj2tZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_EHoxbWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7skmtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+HOME" command="_EHoypWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7spWtZEfCgw5R7bfzv1Q" keySequence="ALT+SHIFT+END" command="_EHfAvmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7szGtZEfCgw5R7bfzv1Q" keySequence="ALT+R" command="_EHoyBmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7slmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_EHozAmtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7sl2tZEfCgw5R7bfzv1Q" keySequence="CTRL+INSERT" command="_EHoxgmtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sq2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" bindingContext="_EHozEWtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7srGtZEfCgw5R7bfzv1Q" keySequence="ESC CTRL+F" command="_EHoxWGtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7sr2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.view.context" bindingContext="_EHoy9GtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7ssGtZEfCgw5R7bfzv1Q" keySequence="Z" command="_EHow02tZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sxmtZEfCgw5R7bfzv1Q" keySequence="+" command="_EHe_sGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sx2tZEfCgw5R7bfzv1Q" keySequence="-" command="_EHe__GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7syGtZEfCgw5R7bfzv1Q" keySequence="/" command="_EHfAYWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s1GtZEfCgw5R7bfzv1Q" keySequence="S" command="_EHowdmtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s1WtZEfCgw5R7bfzv1Q" keySequence="W" command="_EHow4mtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s1mtZEfCgw5R7bfzv1Q" keySequence="A" command="_EHfAlWtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s12tZEfCgw5R7bfzv1Q" keySequence="D" command="_EHow6GtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s2GtZEfCgw5R7bfzv1Q" keySequence="=" command="_EHe_sGtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EH7stWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_EHozCmtZEfCgw5R7bfzv1Q">
    <bindings xmi:id="_EH7stmtZEfCgw5R7bfzv1Q" keySequence="ALT+Y" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7svmtZEfCgw5R7bfzv1Q" keySequence="ALT+A" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sv2tZEfCgw5R7bfzv1Q" keySequence="ALT+B" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7swWtZEfCgw5R7bfzv1Q" keySequence="ALT+C" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7swmtZEfCgw5R7bfzv1Q" keySequence="ALT+D" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sw2tZEfCgw5R7bfzv1Q" keySequence="ALT+E" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sxGtZEfCgw5R7bfzv1Q" keySequence="ALT+F" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sxWtZEfCgw5R7bfzv1Q" keySequence="ALT+G" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7symtZEfCgw5R7bfzv1Q" keySequence="ALT+P" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sy2tZEfCgw5R7bfzv1Q" keySequence="ALT+R" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7szWtZEfCgw5R7bfzv1Q" keySequence="ALT+S" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7szmtZEfCgw5R7bfzv1Q" keySequence="ALT+T" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7sz2tZEfCgw5R7bfzv1Q" keySequence="ALT+V" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s0GtZEfCgw5R7bfzv1Q" keySequence="ALT+W" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s0WtZEfCgw5R7bfzv1Q" keySequence="ALT+H" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s0mtZEfCgw5R7bfzv1Q" keySequence="ALT+L" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
    <bindings xmi:id="_EH7s02tZEfCgw5R7bfzv1Q" keySequence="ALT+N" command="_EHoxMGtZEfCgw5R7bfzv1Q"/>
  </bindingTables>
  <bindingTables xmi:id="_EhMf82tZEfCgw5R7bfzv1Q" bindingContext="_EhMf8mtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMf9WtZEfCgw5R7bfzv1Q" bindingContext="_EhMf9GtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMf92tZEfCgw5R7bfzv1Q" bindingContext="_EhMf9mtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMf-WtZEfCgw5R7bfzv1Q" bindingContext="_EhMf-GtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMf-2tZEfCgw5R7bfzv1Q" bindingContext="_EhMf-mtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMf_WtZEfCgw5R7bfzv1Q" bindingContext="_EhMf_GtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMf_2tZEfCgw5R7bfzv1Q" bindingContext="_EhMf_mtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMgAWtZEfCgw5R7bfzv1Q" bindingContext="_EhMgAGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMgA2tZEfCgw5R7bfzv1Q" bindingContext="_EhMgAmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMgBWtZEfCgw5R7bfzv1Q" bindingContext="_EhMgBGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhMgB2tZEfCgw5R7bfzv1Q" bindingContext="_EhMgBmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWQ8WtZEfCgw5R7bfzv1Q" bindingContext="_EhWQ8GtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWQ82tZEfCgw5R7bfzv1Q" bindingContext="_EhWQ8mtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWQ9WtZEfCgw5R7bfzv1Q" bindingContext="_EhWQ9GtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWQ92tZEfCgw5R7bfzv1Q" bindingContext="_EhWQ9mtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWQ-WtZEfCgw5R7bfzv1Q" bindingContext="_EhWQ-GtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWQ-2tZEfCgw5R7bfzv1Q" bindingContext="_EhWQ-mtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWQ_WtZEfCgw5R7bfzv1Q" bindingContext="_EhWQ_GtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWQ_2tZEfCgw5R7bfzv1Q" bindingContext="_EhWQ_mtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRAWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRAGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRA2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRAmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRBWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRBGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRB2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRBmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRCWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRCGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRC2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRCmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRDWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRDGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRD2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRDmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWREWtZEfCgw5R7bfzv1Q" bindingContext="_EhWREGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRE2tZEfCgw5R7bfzv1Q" bindingContext="_EhWREmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRFWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRFGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRF2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRFmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRGWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRGGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRG2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRGmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRHWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRHGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRH2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRHmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRIWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRIGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRI2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRImtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRJWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRJGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRJ2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRJmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRKWtZEfCgw5R7bfzv1Q" bindingContext="_EhWRKGtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_EhWRK2tZEfCgw5R7bfzv1Q" bindingContext="_EhWRKmtZEfCgw5R7bfzv1Q"/>
  <bindingTables xmi:id="_L_EQYHNUEfCVj4cwJJjSAw" bindingContext="_L_DpUHNUEfCVj4cwJJjSAw"/>
  <rootContext xmi:id="_EG9bC2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_EG9bDGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_EG9bDWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_EHoy52tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_EHoy6GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_EHoy6WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_EHoy6mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_EHoy7mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_EHoy72tZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_EHoy8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_EHoy8WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_EHoy8mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_EHoy82tZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_EHoy9WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_EHoy9mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_EHoy-GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_EHoy-WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_EHozAGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_EHozFGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
        </children>
        <children xmi:id="_EHoy-2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.editor.scope" name="Autoconf Editor" description="Editor for Autoconf Configuration Source Files"/>
        <children xmi:id="_EHozA2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_EHozBGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_EHozC2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.rpm.ui.specEditorScope" name="Specfile Editor Context"/>
        <children xmi:id="_EHozDWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_EHozDmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_EHozD2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_EHozEGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_EHozEmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_EHozEWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" name="ChangeLog Editor"/>
        <children xmi:id="_EHozE2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_EHoy62tZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_EHoy7WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_EHoy-mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_EHoy_WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_EHoy_mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_EHozAmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_EHozBWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_EHozBmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_EHozB2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_EHozCGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_EHozCWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_EHozCmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_EHozDGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_EHozFWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_EHozFmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_EG9bDmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_EHozAWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_EHoy7GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_EHoy9GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.view.context" name="In Time-Based View"/>
  <rootContext xmi:id="_EHoy92tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" name="In Time Graph"/>
  <rootContext xmi:id="_EHoy_GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_EHoy_2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_EhMf8mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_EhMf9GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_EhMf9mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_EhMf-GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_EhMf-mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_EhMf_GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_EhMf_mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_EhMgAGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.cdt.ui.actionSet" name="Auto::org.eclipse.mylyn.cdt.ui.actionSet"/>
  <rootContext xmi:id="_EhMgAmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_EhMgBGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_EhMgBmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_EhWQ8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_EhWQ8mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_EhWQ9GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_EhWQ9mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_EhWQ-GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_EhWQ-mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_EhWQ_GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_EhWQ_mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_EhWRAGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_EhWRAmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_EhWRBGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_EhWRBmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset" name="Auto::org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset"/>
  <rootContext xmi:id="_EhWRCGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_EhWRCmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_EhWRDGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_EhWRDmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_EhWREGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_EhWREmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_EhWRFGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_EhWRFmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_EhWRGGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_EhWRGmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_EhWRHGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_EhWRHmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_EhWRIGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_EhWRImtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_EhWRJGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_EhWRJmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_EhWRKGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_EhWRKmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_L_DpUHNUEfCVj4cwJJjSAw" elementId="ilg.gnumcueclipse.debug.gdbjtag.jlink.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::ilg.gnumcueclipse.debug.gdbjtag.jlink.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_EKjhkGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_EWvjcGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_EWzN0GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EWzN0WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EWzN0mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EWzN02tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EWzN1GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EWzN1WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EWzN1mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EWzN12tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW2RIGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fQGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.testsrunner.resultsview" label="C/C++ Unit" iconURI="platform:/plugin/org.eclipse.cdt.testsrunner/icons/eview16/cppunit.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.testsrunner.internal.ui.view.ResultsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.testsrunner"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fQWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fQmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fQ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fRGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fRWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fRmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fR2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.svg" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fSGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fSWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fSmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fS2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fTGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fTWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fTmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.svg" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fT2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fUGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fUWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fUmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fU2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fVGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fVWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fVmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/hardware_chip.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fV2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/board.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fWGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/info_obj.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fWWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.packs.ui.views.PacksView" label="CMSIS Packs" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/packages.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.PacksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fWmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/outline_co.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fW2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.svg" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_EW3fXGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.dataviewers.charts.view.ChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.dataviewers.charts"/>
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_EW-0AGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_EXACIGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_EXACIWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_EXACImtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_EXACI2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.gcov.view" label="gcov" iconURI="platform:/plugin/org.eclipse.linuxtools.gcov.core/icons/toggle.gif" tooltip="" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gcov.view.CovView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gcov.core"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_EXACJGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gprof.view.GmonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gprof"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_EXACJWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.valgrind.ui.valgrindview" label="Valgrind" iconURI="platform:/plugin/org.eclipse.linuxtools.valgrind.ui/icons/valgrind-icon.png" tooltip="" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.valgrind.ui.ValgrindViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.valgrind.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_EXACJmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_EXACJ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_EXACKGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_EXDFcGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_EXDsgGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_EXETkGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_EXETkWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_EXE6oGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.svg" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_EXE6oWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.svg" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_EXE6omtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_EXE6o2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.presentation.NotificationView" label="Notification" iconURI="platform:/plugin/org.eclipse.oomph.setup.editor/icons/notification.png" tooltip="" allowMultiple="true" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.setup.presentation.NotificationViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.setup.editor"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_EXFhsGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.svg" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_EXGv0GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_EXHW4GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXH98GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.svg" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_EXIlAGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.svg" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_EXIlAWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_EXIlAmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_EXIlA2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.counters.ui.views.countersview" label="Counters" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.counters.ui/icons/counter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.counters.ui.views.CounterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.counters.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXJzIGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.analysis.graph.ui.criticalpath.view.criticalpathview" label="Critical Flow View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.graph.ui/icons/eview16/critical-path.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.graph.ui.criticalpath.view.CriticalPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.graph.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXKaMGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.lami.views.reportview" label="Analysis Report" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.provisional.analysis.lami.ui.views.LamiReportView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.lami.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_EXKaMWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoUGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.scatter" label="Sched_Wakeup/Switch Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoUWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoUmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname" label="Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoU2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoVGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority" label="Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoVWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoVmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.density" label="Sched_Wakeup/Switch Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoV2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.controlflow.ControlFlowView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoWGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.resources.ResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoWWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/cpu-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.cpuusage.CpuUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoWmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoW2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.scatter" label="System Call Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoXGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoXWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.density" label="System Call Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoXmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.kernelmemoryusageview" label="Kernel Memory Usage View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.kernelmemoryusage.KernelMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoX2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.os.linux.views.diskioactivity" label="Disk I/O Activity" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.io.diskioactivity.DiskIOActivityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoYGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Flame Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.profiling.ui.views.flamechart.FlameChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoYWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.callgraphDensity" label="Function Durations Distribution" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/funcdensity.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.CallGraphDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoYmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.flamegraph.flamegraphView" label="Flame Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph.FlameGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoY2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.statistics.callgraphstatistics" label="Function Duration Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.statistics.CallGraphStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoZGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.profiling.ui.flamegraph" label="Flame Graph (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/elcl16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph2.FlameGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoZWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.profiling.ui.flamegraph.selection" label="Flame Graph Selection (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/elcl16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph2.FlameGraphSelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoZmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.profiling.ui.functiondensity" label="Function Durations Distribution (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/elcl16/funcdensity.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.functiondensity.FunctionDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoZ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.profiling.ui.weightedtree" label="Weighted Tree Viewer (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/obj16/stckframe_obj.gif" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.weightedtree.WeightedTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoaGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.profiling.ui.flamechart" label="Flame Chart (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/obj16/stckframe_obj.gif" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.FlameChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoaWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table" label="Segment Store Table" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoamtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics" label="Descriptive Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLoa2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2" label="Segments vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXLobGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLobWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLobmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Matches Scatter Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXLob2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.control.ui.views.ControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.control.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXP5wGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.ust.ui.views.memusage.UstMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXQg0GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXQg0WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXQg0mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/ganttxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.timegraph.XmlTimeGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXRu8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/linechartxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.xychart.XmlXYView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_EXRu8WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latencytable" label="Latency Table" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternLatencyTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXRu8mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.scattergraph" label="Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/scatter.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternScatterGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXRu82tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.density" label="Latency vs Count" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/density.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXRu9GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.statistics" label="Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXRu9WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.timechart.TimeChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWAGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statesystem.TmfStateSystemExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWAWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.colors.ColorsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWAmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.filter.FilterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWA2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.uml2sd.SDView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWBGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.statistics.TmfStatisticsViewImpl"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWBWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.histogram.HistogramView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWBmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.views.eventdensity" label="Event Density" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.eventdensity.EventDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWB2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.synchronization.TmfSynchronizationView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWCGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXSWCWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXULMGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.svg" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_EXUyQGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXVZUGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXWAYGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXWAYWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXWAYmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXWAY2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXWAZGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXWncGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXX1kGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXX1kWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXYcoGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_EXZDsGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_EXZqwGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_EHe_qmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_q2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_rGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHe_rWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_EHe_rmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_EHe_hmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_r2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_sGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin.selection" commandName="Zoom in (selection)" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_sWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_smtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_s2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_tGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_EHe_oGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_tWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_tmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_t2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_uGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_uWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_umtZEfCgw5R7bfzv1Q" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHe_u2tZEfCgw5R7bfzv1Q" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_EHe_vGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_vWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_vmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_v2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_EHe_pWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_wGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_wWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_wmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_EHe_kGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_w2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_xGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.OpenNewViewCommand" commandName="Open New View" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_xWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.application.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_EHe_n2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_xmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_x2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_EHe_n2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_yGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_yWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_ymtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_y2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_zGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.application.command.debugRemoteExecutable" commandName="Debug Remote Executable" description="Debug a Remote executable" category="_EHe_n2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_zWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_EHe_iWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_zmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_z2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableLogger" commandName="Disable Logger" description="Disable Logger" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_0GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_0WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_0mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_EHe_pGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_02tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.analysis.xml.ui.managexmlanalyses" commandName="Manage XML analyses..." description="Manage XML files containing analysis information" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_1GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_EHe_kmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_1WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_1mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_12tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_EHe_f2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_2GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_2WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_2mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_22tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_3GtZEfCgw5R7bfzv1Q" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_EHe_eGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_3WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_3mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_32tZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_4GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_4WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_4mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_42tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_5GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_5WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_5mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_52tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.open_as_experiment" commandName="Open As Experiment..." description="Open selected traces as an experiment" category="_EHe_e2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHe_6GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_EHe_6WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_6mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_62tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_7GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.valgrind.launch.clearMarkersCommand" commandName="Remove Valgrind Markers" description="Removes all Valgrind markers" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_7WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_EHe_qGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_7mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.save" commandName="Save..." description="Save session(s)" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_72tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.application.command.debugAttachedExecutable" commandName="Debug Attached Executable" description="Debug an attached executable" category="_EHe_n2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_8WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_8mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_82tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_9GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_EHe_pmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_9WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_9mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_92tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.changelog.core.actions.KeyActionCommand" commandName="Insert ChangeLog entry" description="Insert a ChangeLog entry" category="_EHe_dmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_-GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_-WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_-mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe_-2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe__GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout.selection" commandName="Zoom out (selection)" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe__WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHe__mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHe__2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_EHfAAGtZEfCgw5R7bfzv1Q" elementId="rpmEditor.toggleComment.command" commandName="Toggle Comment" category="_EHe_nWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAAWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAAmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAA2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfABGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_EHe_mWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfABWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfABmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAB2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfACGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfACWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfACmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAC2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfADGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfADWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfADmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAD2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAEGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_EHe_cmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAEWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAEmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.changelog.core.prepareCommit" commandName="Prepare Commit" description="Copies latest changelog entry to clipboard" category="_EHe_dmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAE2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAFGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.packs.ui.commands.updateCommand" commandName="Refresh" category="_EHe_gGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAFWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAFmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAF2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAGGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.remove" commandName="Remove" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAGWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAGmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.gdbtrace.ui.command.project.trace.selectexecutable" commandName="Select Trace Executable..." description="Select executable binary file for a GDB trace" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAG2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAHGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAHWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAHmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAH2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAIGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHfAIWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_EHfAImtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAI2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAJGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_EHe_jmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAJWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAJmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAJ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAKGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAKWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAKmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAK2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfALGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfALWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfALmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAL2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAMGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAMWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAMmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_EHe_omtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAM2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfANGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfANWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfANmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_EHe_p2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAN2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAOGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAOWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAOmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAO2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAPGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAPWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_EHe_imtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAPmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAP2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAQGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_EHe_e2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHfAQWtZEfCgw5R7bfzv1Q" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_EHfAQmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAQ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfARGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfARWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfARmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAR2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfASGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfASWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfASmtZEfCgw5R7bfzv1Q" elementId="rpmEditor.showOutline.command" commandName="Show Outline" category="_EHe_nWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAS2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfATGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfATWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfATmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAT2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAUGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAUWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_EHe_cmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHfAUmtZEfCgw5R7bfzv1Q" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_EHfAU2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_EHe_f2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAVGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAVWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAVmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAV2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAWGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAWWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAWmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAW2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAXGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAXWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAXmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAX2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHfAYGtZEfCgw5R7bfzv1Q" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_EHfAYWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.filter" commandName="Filter Time Graph events" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAYmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAY2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAZGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_EHe_dGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAZWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_EHe_lmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAZmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAZ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAaGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_EHe_oWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAaWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAamtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAa2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAbGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.logger" commandName="Enable Logger..." description="Assign Logger to Session and Enable Logger" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAbWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAbmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAb2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAcGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.remote.ui.command.fetchlog" commandName="Fetch Remote Traces..." category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAcWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAcmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAc2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAdGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAdWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAdmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAd2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_EHe_eWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAeGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAeWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_EHe_m2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHfAemtZEfCgw5R7bfzv1Q" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_EHfAe2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAfGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAfWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAfmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAf2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAgGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAgWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAgmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_EHe_dGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAg2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_EHe_hmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAhGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAhWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAhmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAh2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAiGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAiWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAimtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAi2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAjGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_EHe_o2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHfAjWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_EHfAjmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAj2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAkGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAkWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_EHe_kmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAkmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAk2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAlGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAlWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.left" commandName="Left" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAlmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAl2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAmGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAmWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_EHe_cmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAmmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAm2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAnGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_EHe_fmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHfAnWtZEfCgw5R7bfzv1Q" elementId="url" name="URL"/>
    <parameters xmi:id="_EHfAnmtZEfCgw5R7bfzv1Q" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_EHfAn2tZEfCgw5R7bfzv1Q" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_EHfAoGtZEfCgw5R7bfzv1Q" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_EHfAoWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAomtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAo2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfApGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfApWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.command.libtoolize" commandName="Invoke Libtoolize" description="Run libtoolize in the selected directory" category="_EHe_gWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfApmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAp2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_EHe_fmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHfAqGtZEfCgw5R7bfzv1Q" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_EHfAqWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.PinViewCommand" commandName="Pin to Debug Context" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAqmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAq2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfArGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfArWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfArmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAr2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAsGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAsWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAsmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.command.autoconf" commandName="Invoke Autoconf" description="Run autoconf in the selected directory" category="_EHe_gWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAs2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAtGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAtWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAtmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAt2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAuGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_add" commandName="Add External Analysis" description="Add External Analysis" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAuWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAumtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAu2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_EHe_kGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAvGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAvWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAvmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_EHe_pGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAv2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAwGtZEfCgw5R7bfzv1Q" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_EHe_eGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAwWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAwmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.editors.text.show.tooltip" commandName="Show Tooltip Description" description="Shows the tooltip description for the element at the cursor" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAw2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAxGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAxWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAxmtZEfCgw5R7bfzv1Q" elementId="rpmlint.toggleRpmlint.command" commandName="Toggle Rpmlint" category="_EHe_fWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAx2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAyGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAyWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAymtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHfAy2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_EHe_jGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowcGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowcWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowcmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowc2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowdGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowdWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowdmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout" commandName="Zoom out (mouse position)" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowd2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoweGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoweWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_EHe_kmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowemtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowe2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowfGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowfWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowfmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowf2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowgGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_EHe_e2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHowgWtZEfCgw5R7bfzv1Q" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_EHowgmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.report_delete" commandName="Delete Report" description="Delete this report from the project" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowg2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_EHe_lWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowhGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowhWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowhmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowh2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowiGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowiWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_EHe_pGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowimtZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_EHe_oWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowi2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowjGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowjWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowjmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_EHe_omtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowj2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowkGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowkWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowkmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowk2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_EHe_hmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowlGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowlWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowlmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowl2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowmGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowmWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowmmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowm2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_EHe_n2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHownGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHownWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHownmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog" commandName="Prepare Changelog" description="Prepares Changelog" category="_EHe_dmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHown2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowoGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowoWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHowomtZEfCgw5R7bfzv1Q" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_EHowo2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowpGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowpWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowpmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.command.automake" commandName="Invoke Automake" description="Run automake from the selected directory" category="_EHe_gWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowp2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowqGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_EHe_oGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowqWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowqmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowq2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_EHe_l2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHowrGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_EHowrWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowrmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowr2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowsGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowsWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowsmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHows2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowtGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowtWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_EHe_jmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowtmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.trim_trace" commandName="Export Time Selection as New Trace..." description="Create a new trace containing only the events in the currently selected time range. Only available if the trace type supports it, and if a time range is selected." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowt2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowuGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowuWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowumtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowu2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_EHe_qGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowvGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowvWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_EHe_e2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHowvmtZEfCgw5R7bfzv1Q" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_EHowv2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowwGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowwWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowwmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoww2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowxGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowxWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowxmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowx2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowyGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.meson.ui.command.runninja" commandName="Run ninja" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowyWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowymtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowy2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_EHe_pmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHowzGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_EHowzWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_EHowzmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHowz2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow0GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow0WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow0mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_EHe_f2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow02tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.zoom.selection" commandName="Zoom to selection" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow1GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow1WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow1mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow12tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow2GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow2WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow2mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow22tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_EHe_iGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow3GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow3WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow3mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_EHe_fmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHow32tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_EHow4GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow4WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow4mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin" commandName="Zoom in (mouse position)" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow42tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_EHe_m2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHow5GtZEfCgw5R7bfzv1Q" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_EHow5WtZEfCgw5R7bfzv1Q" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_EHow5mtZEfCgw5R7bfzv1Q" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_EHow52tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow6GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.right" commandName="Right" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow6WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow6mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow62tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow7GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_EHe_f2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow7WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow7mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow72tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_EHe_nmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_EHe_mWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow8WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow8mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow82tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHow9GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_EHow9WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow9mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_EHe_g2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHow92tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_EHow-GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_EHow-WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_EHe_l2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHow-mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_EHow-2tZEfCgw5R7bfzv1Q" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow_GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow_WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow_mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHow_2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_EHe_hWtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxAGtZEfCgw5R7bfzv1Q" elementId="title" name="Title"/>
    <parameters xmi:id="_EHoxAWtZEfCgw5R7bfzv1Q" elementId="message" name="Message"/>
    <parameters xmi:id="_EHoxAmtZEfCgw5R7bfzv1Q" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_EHoxA2tZEfCgw5R7bfzv1Q" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_EHoxBGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.load" commandName="Load..." description="Load session(s)" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxBWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxBmtZEfCgw5R7bfzv1Q" elementId="rpmlint.runRpmlint.command" commandName="Run rpmlint" category="_EHe_fWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxB2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_EHe_qWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxCGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxCWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxCmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.command.aclocal" commandName="Invoke Aclocal" description="Run aclocal from the selected directory" category="_EHe_gWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxC2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_EHe_pGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxDGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxDWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxDmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.command.autoheader" commandName="Invoke Autoheader" description="Run autoheader from the selected directory" category="_EHe_gWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxD2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxEGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxEWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxEmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_EHe_oWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxE2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxFGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxFWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.command.autoreconf" commandName="Invoke Autoreconf" description="Run autoreconf from the selected directory" category="_EHe_gWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxFmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxF2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxGGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxGWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxGmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxG2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_EHe_n2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxHGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_EHe_mWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxHWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_EHe_fmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxHmtZEfCgw5R7bfzv1Q" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_EHoxH2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxIGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxIWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxImtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_EHe_cGtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxI2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_EHoxJGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxJWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxJmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxJ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxKGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxKWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxKmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxK2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxLGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxLWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxLmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxL2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxMGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_EHe_oGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxMWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxMmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxM2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxNGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxNWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxNmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxN2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxOGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_EHe_qWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxOWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxOmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxO2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxPGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxPWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxPmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_EHe_cmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxP2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxQGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxQWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxQmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.timegraph.bookmark" commandName="Toggle Bookmark..." category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxQ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxRGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxRWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxRmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxR2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxSGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxSWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_EHe_f2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxSmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_EHe_o2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxS2tZEfCgw5R7bfzv1Q" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_EHoxTGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxTWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxTmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxT2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.application.command.debugCore" commandName="Debug Core File" description="Debug a corefile" category="_EHe_n2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxUGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxUWtZEfCgw5R7bfzv1Q" elementId="rpmEditor.prepareSources.command" commandName="Prepare Sources" category="_EHe_nWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxUmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxU2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxVGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_EHe_fGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxVWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_EHe_kGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxVmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_EHe_dGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxV2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxWGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.changelog.core.formatChangeLog" commandName="Format ChangeLog" description="Formats ChangeLog" category="_EHe_dmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxWWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxWmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxW2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxXGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_EHe_jWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxXWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxXmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxX2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxYGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxYWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxYmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxY2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxZGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxZWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.bugs.commands.ReportBugAction" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement for predefined Products / Projects" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxZmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxZ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxaGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxaWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_EHoxamtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxa2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxbGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxbWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_EHe_pGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxbmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxb2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxcGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxcWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxcmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxc2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxdGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxdWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxdmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxd2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxeGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxeWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxemtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxe2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxfGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxfWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_EHe_qGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxfmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_EHe_gmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxf2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxgGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxgWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_EHe_pWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxgmtZEfCgw5R7bfzv1Q" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_EHe_eGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxg2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxhGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxhWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxhmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxh2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.command.showOutline" commandName="Show Outline" category="_EHe_gWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxiGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxiWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoximtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxi2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxjGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxjWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxjmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxj2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxkGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxkWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxkmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxk2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_EHe_oWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxlGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxlWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_EHe_p2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxlmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxl2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxmGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_EHe_qGtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxmWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_EHoxmmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxm2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxnGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_EHe_j2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxnWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_EHoxnmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_EHoxn2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_EHoxoGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_EHe_f2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxoWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.notifications" commandName="Notifications" description="Notifications for the Eclipse IDE" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxomtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_EHe_gmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxo2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxpGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxpWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_EHe_l2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxpmtZEfCgw5R7bfzv1Q" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_EHoxp2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxqGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxqWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxqmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxq2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxrGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.valgrind.launch.exportCommand" commandName="Export Valgrind Log Files" description="Exports Valgrind log output to a directory" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxrWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxrmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_EHe_d2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxr2tZEfCgw5R7bfzv1Q" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_EHoxsGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxsWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxsmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_EHe_cmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxs2tZEfCgw5R7bfzv1Q" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_EHoxtGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxtWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_EHe_m2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoxtmtZEfCgw5R7bfzv1Q" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_EHoxt2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxuGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxuWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxumtZEfCgw5R7bfzv1Q" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxu2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxvGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_EHe_omtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxvWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxvmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxv2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxwGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces..." description="Synchronize 2 or more traces" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxwWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxwmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxw2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxxGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxxWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxxmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_EHe_n2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxx2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxyGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxyWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_EHe_oWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxymtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableLogger" commandName="Enable Logger" description="Enable Logger" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxy2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxzGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxzWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_EHe_cmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxzmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_EHe_mWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoxz2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeCommand" commandName="Instruction Stepping Mode" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox0GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox0WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog2" commandName="Prepare Changelog In Editor" description="Prepares ChangeLog in an editor" category="_EHe_dmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox0mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox02tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox1GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox1WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox1mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox12tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox2GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox2WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox2mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox22tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox3GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox3WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox3mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox32tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox4GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_EHe_omtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox4WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox4mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox42tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox5GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox5WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox5mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox52tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox6GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_EHe_oGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox6WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox6mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox62tZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox7GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox7WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_EHe_pmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHox7mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_EHox72tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox8GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox8WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox8mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox82tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox9GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox9WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_EHe_f2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox9mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox92tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox-GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.cdt.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_EHe_emtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox-WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox-mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox-2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox_GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox_WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_EHe_qGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox_mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHox_2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyAGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyAWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyAmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyA2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyBGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyBWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyBmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_EHe_pGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyB2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_EHe_oWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyCGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyCWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyCmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyC2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyDGtZEfCgw5R7bfzv1Q" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyDWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyDmtZEfCgw5R7bfzv1Q" elementId="rpmEditor.organizePatches.command" commandName="Organize Patches" category="_EHe_nWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyD2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyEGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyEWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyEmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyE2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyFGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_EHe_f2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyFWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_EHe_jWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyFmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyF2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyGGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyGWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyGmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyG2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_EHe_pWtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoyHGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_EHoyHWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_EHoyHmtZEfCgw5R7bfzv1Q" elementId="rpmEditor.build.command" commandName="RPM Build Command" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoyH2tZEfCgw5R7bfzv1Q" elementId="buildType" name="buildType" optional="false"/>
    <parameters xmi:id="_EHoyIGtZEfCgw5R7bfzv1Q" elementId="actOnEditor" name="actOnEditor"/>
  </commands>
  <commands xmi:id="_EHoyIWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyImtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyI2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyJGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyJWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyJmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyJ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyKGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyKWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyKmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_EHe_nGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyK2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.problem" commandName="Report a Problem" description="Report a problem for this IDE" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyLGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyLWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyLmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyL2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyMGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyMWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyMmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyM2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_EHe_pmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyNGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_EHe_e2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyNWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyNmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyN2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyOGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyOWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyOmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyO2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyPGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyPWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyPmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyP2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyQGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyQWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.launchbar.ui.command.openLaunchSelector" commandName="Open Launch Bar Config Selector" category="_EHe_omtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyQmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyQ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyRGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyRWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyRmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyR2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoySGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoySWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoySmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyS2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_EHe_hWtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoyTGtZEfCgw5R7bfzv1Q" elementId="title" name="Title"/>
    <parameters xmi:id="_EHoyTWtZEfCgw5R7bfzv1Q" elementId="message" name="Message"/>
    <parameters xmi:id="_EHoyTmtZEfCgw5R7bfzv1Q" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_EHoyT2tZEfCgw5R7bfzv1Q" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_EHoyUGtZEfCgw5R7bfzv1Q" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_EHoyUWtZEfCgw5R7bfzv1Q" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_EHoyUmtZEfCgw5R7bfzv1Q" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_EHoyU2tZEfCgw5R7bfzv1Q" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_EHoyVGtZEfCgw5R7bfzv1Q" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_EHoyVWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyVmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyV2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.packs.ui.commands.showPerspectiveCommand" commandName="Switch to CMSIS Packs Perspective" category="_EHe_gGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyWGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyWWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyWmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyW2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyXGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_EHe_jWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyXWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.command.reconfigure" commandName="Reconfigure Project" description="Run configuration scripts for project" category="_EHe_gWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyXmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyX2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyYGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyYWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyYmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyY2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyZGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyZWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyZmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyZ2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyaGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoyaWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_EHoyamtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_EHoya2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoybGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoybWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoybmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_EHe_jWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyb2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoycGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoycWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_EHe_qWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoycmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyc2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoydGtZEfCgw5R7bfzv1Q" elementId="rpmEditor.downloadSources.command" commandName="Download Sources" category="_EHe_nWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoydWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoydmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_EHe_h2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoyd2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_EHoyeGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyeWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_EHe_lGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyemtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoye2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyfGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyfWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyfmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_EHe_cmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyf2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoygGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoygWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_EHe_jmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoygmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyg2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyhGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyhWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_EHe_oGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyhmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.convert_project" commandName="Configure or convert to Tracing Project" description="Configure or convert project to tracing project" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyh2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyiGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyiWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyimtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyi2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyjGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyjWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyjmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyj2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoykGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_EHe_dGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoykWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoykmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyk2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoylGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_EHe_mWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoylWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoylmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyl2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_EHe_iWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoymGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoymWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoymmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoym2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoynGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoynWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoynmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyn2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_EHe_d2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyoGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyoWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyomtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyo2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoypGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_EHe_jWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoypWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_EHe_pGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoypmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_EHe_mGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyp2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyqGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyqWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyqmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyq2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyrGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyrWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoyrmtZEfCgw5R7bfzv1Q" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_EHoyr2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoysGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoysWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_EHe_fmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoysmtZEfCgw5R7bfzv1Q" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_EHoys2tZEfCgw5R7bfzv1Q" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_EHoytGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoytWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoytmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_EHe_m2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyt2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyuGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_EHe_iWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyuWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_EHe_l2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyumtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyu2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyvGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyvWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyvmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_EHe_e2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoyv2tZEfCgw5R7bfzv1Q" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_EHoywGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoywWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_EHe_cmtZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoywmtZEfCgw5R7bfzv1Q" elementId="kind" name="Kind"/>
    <parameters xmi:id="_EHoyw2tZEfCgw5R7bfzv1Q" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_EHoyxGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyxWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_EHe_cmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyxmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyx2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyyGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyyWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyymtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyy2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_EHe_dWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyzGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyzWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_EHe_o2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyzmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoyz2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy0GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_EHe_i2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoy0WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_EHoy0mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_EHoy02tZEfCgw5R7bfzv1Q" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_remove" commandName="Remove External Analysis" description="Remove External Analysis" category="_EHe_h2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy1GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_EHe_mmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy1WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy1mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy12tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy2GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy2WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy2mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy22tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy3GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy3WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_EHe_hGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy3mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_EHe_c2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy32tZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_EHe_cGtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy4GtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_EHe_fmtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy4WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy4mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy42tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_EHe_l2tZEfCgw5R7bfzv1Q">
    <parameters xmi:id="_EHoy5GtZEfCgw5R7bfzv1Q" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_EHoy5WtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_EHe_g2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EHoy5mtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_EHe_kWtZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmEGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmEWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmEmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmE2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmFGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmFWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmFmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmF2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmGGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmGWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmGmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmG2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmHGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmHWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWOmHmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwAGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwAWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwAmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwA2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwBGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwBWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwBmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwB2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwCGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwCWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwCmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwC2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwDGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwDWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwDmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwD2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwEGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwEWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwEmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwE2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwFGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwFWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwFmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwF2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwGGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwGWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwGmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwG2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwHGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwHWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwHmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwH2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwIGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwIWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwImtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwI2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwJGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwJWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwJmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwJ2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwKGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwKWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwKmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwK2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwLGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwLWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwLmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwL2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwMGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.cdt.ui.cview.contribution/org.eclipse.mylyn.cdt.ui.cview.focusActiveTask.action" commandName="Focus on Active Task" description="Focus only on elements in active Mylyn task" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwMWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwMmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwM2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwNGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwNWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwNmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwN2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwOGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwOWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwOmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwO2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwPGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwPWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwPmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwP2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwQGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwQWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwQmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwQ2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwRGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwRWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwRmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwR2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwSGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwSWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwSmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwS2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwTGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwTWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwTmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwT2tZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwUGtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwUWtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <commands xmi:id="_EWXwUmtZEfCgw5R7bfzv1Q" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_EHe_i2tZEfCgw5R7bfzv1Q"/>
  <addons xmi:id="_EG9bD2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_EG9bEGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_EG9bEWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_EG9bEmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_EG9bE2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_EG9bFGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_EG9bFWtZEfCgw5R7bfzv1Q" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_EG9bFmtZEfCgw5R7bfzv1Q" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_EG9bF2tZEfCgw5R7bfzv1Q" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_EG9bGGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_EHPu4GtZEfCgw5R7bfzv1Q" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_DkLzIXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_EHe_cGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_EHe_cWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_EHe_cmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_EHe_c2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_EHe_dGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_EHe_dWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_EHe_dmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.changelog" name="Changelog" description="Changelog key bindings"/>
  <categories xmi:id="_EHe_d2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_EHe_eGtZEfCgw5R7bfzv1Q" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_EHe_eWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_EHe_emtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.cdt.ui.commands" name="CDT Context" description="CDT Task-Focused Interface Commands"/>
  <categories xmi:id="_EHe_e2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_EHe_fGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_EHe_fWtZEfCgw5R7bfzv1Q" elementId="rpmlint.category" name="Rpmlint Commands" description="Specfile Editor Commands"/>
  <categories xmi:id="_EHe_fmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_EHe_f2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_EHe_gGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.packs.ui.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_EHe_gWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.autotools.ui.category.invokeAutotools" name="Invoke Autotools"/>
  <categories xmi:id="_EHe_gmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_EHe_g2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_EHe_hGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_EHe_hWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_EHe_hmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_EHe_h2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_EHe_iGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_EHe_iWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_EHe_imtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_EHe_i2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_EHe_jGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_EHe_jWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
  <categories xmi:id="_EHe_jmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_EHe_j2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_EHe_kGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_EHe_kWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.ui.category.source" name="Source" description="Source commands"/>
  <categories xmi:id="_EHe_kmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_EHe_k2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_EHe_lGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_EHe_lWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.category" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_EHe_lmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_EHe_l2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_EHe_mGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_EHe_mWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_EHe_mmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_EHe_m2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_EHe_nGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_EHe_nWtZEfCgw5R7bfzv1Q" elementId="rpmEditor.category" name="Editor Commands" description="Specfile Editor Commands"/>
  <categories xmi:id="_EHe_nmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_EHe_n2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_EHe_oGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_EHe_oWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_EHe_omtZEfCgw5R7bfzv1Q" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_EHe_o2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_EHe_pGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_EHe_pWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_EHe_pmtZEfCgw5R7bfzv1Q" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_EHe_p2tZEfCgw5R7bfzv1Q" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_EHe_qGtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_EHe_qWtZEfCgw5R7bfzv1Q" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
</application:Application>
