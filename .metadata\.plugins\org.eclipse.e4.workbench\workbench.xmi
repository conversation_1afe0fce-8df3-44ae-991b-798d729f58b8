<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_wYY3MHNyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_wYY3MXNyEfCIHPp1rUlC6w" bindingContexts="_wYZiQnNyEfCIHPp1rUlC6w">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;System.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/System.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/System.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;cmsis_gcc.h&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/Platform/Core/cmsis_gcc.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/ZhiXinSDK/Platform/Core/cmsis_gcc.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;TIMT.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/TIMT.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/TIMT.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;System.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/System.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/System.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;RTSC.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/RTSC.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/RTSC.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;RTSC.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/RTSC.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/RTSC.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;PWMT.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/PWMT.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/PWMT.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;PWMT.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/PWMT.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/PWMT.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;PWMT_Cfg.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/PWMT_Cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/PWMT_Cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Compiler.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/Compiler.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/Compiler.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Compiler_Cfg.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/Compiler_Cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/Compiler_Cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;CLKT.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/CLKT.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/CLKT.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;CLKT.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/CLKT.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/CLKT.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;TIMT.h&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/TIMT.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/TIMT.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;OS.c&quot; tooltip=&quot;48V code/03_BSW/System/01_Service/OS.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/01_Service/OS.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;48V code/03_BSW/STAR/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/STAR/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;OS.h&quot; tooltip=&quot;48V code/03_BSW/System/01_Service/OS.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/01_Service/OS.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;EcuM.h&quot; tooltip=&quot;48V code/03_BSW/System/01_Service/EcuM.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/01_Service/EcuM.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Wdgif.h&quot; tooltip=&quot;48V code/03_BSW/System/02_HAL/Wdgif.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/02_HAL/Wdgif.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Gpt.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/Gpt.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/System/03_MCAL/Gpt.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Z20K11xM_stim.c&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;Z20K116M_startup.S&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;NvM.c&quot; tooltip=&quot;48V code/03_BSW/Memory/01_Service/NvM.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/Memory/01_Service/NvM.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform_cfg.h&quot; tooltip=&quot;48V code/03_BSW/ZhiXinSDK/MCU/platform_cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03_BSW/ZhiXinSDK/MCU/platform_cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;NvM.c&quot; tooltip=&quot;48V code/03-BSW/Memory/NvM.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/NvM.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;MemIf.c&quot; tooltip=&quot;48V code/03-BSW/Memory/MemIf.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/MemIf.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Fee.c&quot; tooltip=&quot;48V code/03-BSW/Memory/Fee.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/Fee.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;MemIf.h&quot; tooltip=&quot;48V code/03-BSW/Memory/MemIf.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/MemIf.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Fee.h&quot; tooltip=&quot;48V code/03-BSW/Memory/Fee.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/Fee.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Fls.c&quot; tooltip=&quot;48V code/03-BSW/Memory/Fls.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/Fls.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;OS.c&quot; tooltip=&quot;48V code/03-BSW/System/OS.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/OS.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;OS.h&quot; tooltip=&quot;48V code/03-BSW/System/OS.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/OS.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;NvM.h&quot; tooltip=&quot;48V code/03-BSW/Memory/NvM.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/Memory/NvM.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;48V code/07-source/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/07-source/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Wdgm.c&quot; tooltip=&quot;48V code/03-BSW/System/Wdgm.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/Wdgm.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Pwm.c&quot; tooltip=&quot;48V code/03-BSW/IO/Pwm.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/Pwm.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Mcu.c&quot; tooltip=&quot;48V code/03-BSW/System/Mcu.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/Mcu.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;AdcIf.c&quot; tooltip=&quot;48V code/03-BSW/IO/AdcIf.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/AdcIf.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;AdcIf.h&quot; tooltip=&quot;48V code/03-BSW/IO/AdcIf.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/AdcIf.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Adc.c&quot; tooltip=&quot;48V code/03-BSW/IO/Adc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/Adc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Dio.c&quot; tooltip=&quot;48V code/03-BSW/IO/Dio.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/IO/Dio.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;core_cm0plus.h&quot; tooltip=&quot;48V code/ZhixinSDK/Platform/Core/core_cm0plus.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/ZhixinSDK/Platform/Core/core_cm0plus.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Gpt.c&quot; tooltip=&quot;48V code/03-BSW/System/Gpt.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/03-BSW/System/Gpt.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform_cfg.h&quot; tooltip=&quot;48V code/ZhixinSDK/Platform/platform_cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/ZhixinSDK/Platform/platform_cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;core_cm0plus.h&quot; tooltip=&quot;48V code/ZhixinSDK/CORE/core_cm0plus.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/ZhixinSDK/CORE/core_cm0plus.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;Z20K11xM_adc.c&quot; tooltip=&quot;48V code/ZhixinSDK/StdDriver/Src/Z20K11xM_adc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/ZhixinSDK/StdDriver/Src/Z20K11xM_adc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;HeatingControl.h&quot; tooltip=&quot;48V code/01-SWC/HMC/HeatingControl.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/01-SWC/HMC/HeatingControl.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;arm_compat.h&quot; tooltip=&quot;48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/0_Core/arm_compat.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/0_Core/arm_compat.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;platform_cfg.h&quot; tooltip=&quot;48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/1_MCU/platform_cfg.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/1_MCU/platform_cfg.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;FastIRQ.c&quot; tooltip=&quot;48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/7_Source/FastIRQ.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/7_Source/FastIRQ.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;core_cm0plus.h&quot; tooltip=&quot;48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/0_Core/core_cm0plus.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/48V code/SDK/Z20K118MC_Eclipse/Z20K118MC/0_Core/core_cm0plus.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_wYY3MXNyEfCIHPp1rUlC6w" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_wYY3MnNyEfCIHPp1rUlC6w" label="%trimmedwindow.label.eclipseSDK" x="0" y="0" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1753669076430"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_wYY3MnNyEfCIHPp1rUlC6w" selectedElement="_wYY3M3NyEfCIHPp1rUlC6w" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_wYY3M3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_wYY3SXNyEfCIHPp1rUlC6w">
        <children xsi:type="advanced:Perspective" xmi:id="_wYY3NHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_wYY3NXNyEfCIHPp1rUlC6w" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.autotools.ui.wizards.conversionWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.profileActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.perspSC:org.eclipse.embedcdt.internal.codered.ui.perspectives.CodeRedPerspective</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_wYY3NXNyEfCIHPp1rUlC6w" selectedElement="_wYY3OnNyEfCIHPp1rUlC6w" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_wYY3NnNyEfCIHPp1rUlC6w" elementId="topLeft" containerData="1609" selectedElement="_wYY3N3NyEfCIHPp1rUlC6w">
              <children xsi:type="advanced:Placeholder" xmi:id="_wYY3N3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_wYZfA3NyEfCIHPp1rUlC6w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_wYY3OHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_wYZfWHNyEfCIHPp1rUlC6w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_wYY3OXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_wYZfWXNyEfCIHPp1rUlC6w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_wYY3OnNyEfCIHPp1rUlC6w" containerData="8391" selectedElement="_wYY3Q3NyEfCIHPp1rUlC6w">
              <children xsi:type="basic:PartSashContainer" xmi:id="_wYY3O3NyEfCIHPp1rUlC6w" containerData="7500" selectedElement="_wYY3PHNyEfCIHPp1rUlC6w" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3PHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_wYZe_XNyEfCIHPp1rUlC6w"/>
                <children xsi:type="basic:PartStack" xmi:id="_wYY3PXNyEfCIHPp1rUlC6w" elementId="topRight" containerData="2500" selectedElement="_wYY3PnNyEfCIHPp1rUlC6w">
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3PnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ContentOutline" ref="_wYZfgnNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3P3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_wYZfhXNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3QHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_wYZfhnNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Mylyn</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3QXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_wYZfiHNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3QnNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" ref="_wYZfiXNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:CMSIS Packs</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_wYY3Q3NyEfCIHPp1rUlC6w" elementId="bottom" containerData="2500" selectedElement="_wYY3RnNyEfCIHPp1rUlC6w">
                <tags>Oomph</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3RHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProblemView" ref="_wYZfWnNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3RXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.TaskList" ref="_wYZfXXNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3RnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.console.ConsoleView" ref="_wYZfYHNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3R3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.PropertySheet" ref="_wYZfgXNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3SHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_wYZfh3NyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_wYY3SXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_wYY3SnNyEfCIHPp1rUlC6w" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.svg">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.xml.ui.perspective</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser</tags>
          <tags>persp.perspSC:org.eclipse.embedcdt.internal.codered.ui.perspectives.CodeRedPerspective</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.debuggerConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.debugsources.view</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.svg</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_wYY3SnNyEfCIHPp1rUlC6w" selectedElement="_wYY3UXNyEfCIHPp1rUlC6w" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_wYY3S3NyEfCIHPp1rUlC6w" containerData="1560" selectedElement="_wYY3THNyEfCIHPp1rUlC6w" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_wYY3THNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_wYY3TnNyEfCIHPp1rUlC6w">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3TXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.DebugView" ref="_wYZfinNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3TnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_wYZfA3NyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                  <tags>active</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_wYY3T3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3UHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_wYZf6XNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_wYY3UXNyEfCIHPp1rUlC6w" containerData="8440" selectedElement="_wYY3UnNyEfCIHPp1rUlC6w">
              <children xsi:type="basic:PartSashContainer" xmi:id="_wYY3UnNyEfCIHPp1rUlC6w" containerData="7500" selectedElement="_wYY3U3NyEfCIHPp1rUlC6w" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3U3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editorss" containerData="6500" ref="_wYZe_XNyEfCIHPp1rUlC6w"/>
                <children xsi:type="basic:PartStack" xmi:id="_wYY3VHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="3500" selectedElement="_wYY3V3NyEfCIHPp1rUlC6w">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3VXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.VariableView" ref="_wYZfuHNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3VnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.BreakpointView" ref="_wYZfu3NyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3V3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.ExpressionView" ref="_wYZfvnNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3WHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_wYZfgnNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3WXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_wYZfgXNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3WnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_wYZfhXNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3W3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_wYZf5XNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3XHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_wYZf5nNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3XXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" toBeRendered="false" ref="_wYZf53NyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_wYY3XnNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" ref="_wYZf7nNyEfCIHPp1rUlC6w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_wYY3X3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="2500" selectedElement="_wYY3YHNyEfCIHPp1rUlC6w">
                <tags>Debug</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3YHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.console.ConsoleView" ref="_wYZfYHNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3YXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.RegisterView" ref="_wYZftHNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3YnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_wYZfWXNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3Y3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_wYZft3NyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3ZHNyEfCIHPp1rUlC6w" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_wYZf5HNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3ZXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProblemView" ref="_wYZfWnNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3ZnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_wYZfh3NyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3Z3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" toBeRendered="false" ref="_wYZf6HNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3aHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" ref="_wYZf6nNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3aXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" toBeRendered="false" ref="_wYZf7XNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3anNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.MemoryView" ref="_wYZf8XNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_wYY3a3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_wYZf9HNyEfCIHPp1rUlC6w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_wYY3bHNyEfCIHPp1rUlC6w" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_wYY3bXNyEfCIHPp1rUlC6w" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_wYZe-HNyEfCIHPp1rUlC6w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_wYY3bnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_wYZe-XNyEfCIHPp1rUlC6w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_wYY3b3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_wYZe_HNyEfCIHPp1rUlC6w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZe-HNyEfCIHPp1rUlC6w" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZe-XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_wYZe-nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZe-3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZe_HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_wYZe_XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editorss" selectedElement="_wYZe_nNyEfCIHPp1rUlC6w">
      <children xsi:type="basic:PartStack" xmi:id="_wYZe_nNyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_wYZe_3NyEfCIHPp1rUlC6w">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <children xsi:type="basic:Part" xmi:id="_wYZe_3NyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;48V code/03_BSW/System/03_MCAL/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/48V code/03_BSW/System/03_MCAL/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1697&quot; selectionTopPixel=&quot;756&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>active</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfA3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Aggregate for window 1753669076430&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_wYZfBHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZfUXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfWHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfWXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfWnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_wYZfW3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZfXHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfXXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.priorityField=&quot;30&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_wYZfXnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZfX3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfYHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_wYZfYXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZfZHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfgXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfgnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_wYZfg3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZfhHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfhXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfhnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfh3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfiHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfiXNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
      <tags>View</tags>
      <tags>categoryTag:CMSIS Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfinNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZfi3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZfm3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZftHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZftXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZftnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZft3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfuHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZfuXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZfunNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfu3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZfvHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZfvXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZfvnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZfv3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZf0nNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.ExpressionView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf5HNyEfCIHPp1rUlC6w" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf5XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf5nNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf53NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf6HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf6XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf6nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZf63NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZf7HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf7XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf7nNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZf73NyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZf8HNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf8XNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZf8nNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZf83NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_wYZf9HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_wYZf9XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.executablesView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_wYZf9nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.executablesView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_wYZf93NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_wYZf-HNyEfCIHPp1rUlC6w" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_wYZf-XNyEfCIHPp1rUlC6w" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZf-nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_wYZgAnNyEfCIHPp1rUlC6w" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.svg" tooltip="Print" command="_wYbWVnNyEfCIHPp1rUlC6w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgCHNyEfCIHPp1rUlC6w" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_wYZgCXNyEfCIHPp1rUlC6w" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgCnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_wYZgDHNyEfCIHPp1rUlC6w" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.svg" tooltip="Undo" enabled="false" command="_wYbUj3NyEfCIHPp1rUlC6w"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_wYZgDXNyEfCIHPp1rUlC6w" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.svg" tooltip="Redo" enabled="false" command="_wYbVDXNyEfCIHPp1rUlC6w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgDnNyEfCIHPp1rUlC6w" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_wYZgD3NyEfCIHPp1rUlC6w" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgVXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgVnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgW3NyEfCIHPp1rUlC6w" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgYHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgZnNyEfCIHPp1rUlC6w" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_wYZgZ3NyEfCIHPp1rUlC6w" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgaHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_wYZgbnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.svg" tooltip="Pin Editor" type="Check" command="_wYbWBHNyEfCIHPp1rUlC6w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgc3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.editor.CEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgdHNyEfCIHPp1rUlC6w" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_wYZgdXNyEfCIHPp1rUlC6w" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgdnNyEfCIHPp1rUlC6w" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_wYZgd3NyEfCIHPp1rUlC6w" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_wYZgeHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_wYZge3NyEfCIHPp1rUlC6w" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_wYZgf3NyEfCIHPp1rUlC6w" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_wYZghnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_wYZgh3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_wYZgiHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_wYZgiXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_wYZgkXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_wYZgknNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_wYZgk3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_wYZglHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_wYZglXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.OutlineFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_wYZglnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <bindingTables xmi:id="_wYZgl3NyEfCIHPp1rUlC6w" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_wYZiQnNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZgmHNyEfCIHPp1rUlC6w" keySequence="CTRL+1" command="_wYbUTHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgmXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+L" command="_wYbWjXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgmnNyEfCIHPp1rUlC6w" keySequence="CTRL+V" command="_wYbTp3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgm3NyEfCIHPp1rUlC6w" keySequence="CTRL+A" command="_wYbU7HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgnHNyEfCIHPp1rUlC6w" keySequence="CTRL+C" command="_wYbVQnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgnXNyEfCIHPp1rUlC6w" keySequence="CTRL+X" command="_wYbUlnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgnnNyEfCIHPp1rUlC6w" keySequence="CTRL+Y" command="_wYbVDXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgn3NyEfCIHPp1rUlC6w" keySequence="CTRL+Z" command="_wYbUj3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgoHNyEfCIHPp1rUlC6w" keySequence="ALT+PAGE_UP" command="_wYbVF3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgoXNyEfCIHPp1rUlC6w" keySequence="ALT+PAGE_DOWN" command="_wYbVz3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgonNyEfCIHPp1rUlC6w" keySequence="SHIFT+INSERT" command="_wYbTp3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgo3NyEfCIHPp1rUlC6w" keySequence="ALT+F11" command="_wYbT6HNyEfCIHPp1rUlC6w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_wYZgpHNyEfCIHPp1rUlC6w" keySequence="CTRL+F10" command="_wYbTy3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgpXNyEfCIHPp1rUlC6w" keySequence="CTRL+INSERT" command="_wYbVQnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgpnNyEfCIHPp1rUlC6w" keySequence="CTRL+PAGE_UP" command="_wYbWa3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgp3NyEfCIHPp1rUlC6w" keySequence="CTRL+PAGE_DOWN" command="_wYbUVHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgqHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+F1" command="_wYbT_HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgqXNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+F2" command="_wYbVt3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgqnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+F3" command="_wYbWYHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgq3NyEfCIHPp1rUlC6w" keySequence="SHIFT+DEL" command="_wYbUlnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgrHNyEfCIHPp1rUlC6w" keySequence="ALT+/" command="_wYbWKnNyEfCIHPp1rUlC6w">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_wYZgrXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.textEditorScope" bindingContext="_wYZiR3NyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZgrnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+CR" command="_wYbWX3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgr3NyEfCIHPp1rUlC6w" keySequence="CTRL+BS" command="_wYbTfHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgsHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+Q" command="_wYbUM3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgsXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+C" command="_wYbTvXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgsnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+J" command="_wYbUJHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgs3NyEfCIHPp1rUlC6w" keySequence="CTRL++" command="_wYbVrnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgtHNyEfCIHPp1rUlC6w" keySequence="CTRL+-" command="_wYbU03NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgtXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+P" command="_wYbV83NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgtnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+C" command="_wYbTiHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgt3NyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+V" command="_wYbT2HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZguHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F" command="_wYbUd3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZguXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+J" command="_wYbUQnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgunNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+A" command="_wYbVYXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgu3NyEfCIHPp1rUlC6w" keySequence="CTRL+T" command="_wYbWyHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgvHNyEfCIHPp1rUlC6w" keySequence="CTRL+J" command="_wYbT0nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgvXNyEfCIHPp1rUlC6w" keySequence="CTRL+L" command="_wYbWQ3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgvnNyEfCIHPp1rUlC6w" keySequence="CTRL+O" command="_wYbWq3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgv3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+/" command="_wYbUgXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgwHNyEfCIHPp1rUlC6w" keySequence="CTRL+D" command="_wYbT3HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgwXNyEfCIHPp1rUlC6w" keySequence="CTRL+=" command="_wYbVrnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgwnNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+/" command="_wYbWn3NyEfCIHPp1rUlC6w">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_wYZgw3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Y" command="_wYbTcXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgxHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+DEL" command="_wYbWL3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgxXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+X" command="_wYbVS3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgxnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+Y" command="_wYbU0XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgx3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+\" command="_wYbVd3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgyHNyEfCIHPp1rUlC6w" keySequence="CTRL+DEL" command="_wYbUinNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgyXNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_UP" command="_wYbW8XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgynNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_DOWN" command="_wYbV23NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgy3NyEfCIHPp1rUlC6w" keySequence="SHIFT+END" command="_wYbU2XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgzHNyEfCIHPp1rUlC6w" keySequence="SHIFT+HOME" command="_wYbUv3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgzXNyEfCIHPp1rUlC6w" keySequence="END" command="_wYbWeHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgznNyEfCIHPp1rUlC6w" keySequence="INSERT" command="_wYbVhXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZgz3NyEfCIHPp1rUlC6w" keySequence="F2" command="_wYbUVnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg0HNyEfCIHPp1rUlC6w" keySequence="HOME" command="_wYbWmnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg0XNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+ARROW_UP" command="_wYbWynNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg0nNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+ARROW_DOWN" command="_wYbU8nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg03NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+INSERT" command="_wYbUDXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg1HNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_wYbU23NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg1XNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_wYbUFXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg1nNyEfCIHPp1rUlC6w" keySequence="CTRL+F10" command="_wYbWWnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg13NyEfCIHPp1rUlC6w" keySequence="CTRL+END" command="_wYbV3XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg2HNyEfCIHPp1rUlC6w" keySequence="CTRL+ARROW_UP" command="_wYbT-nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg2XNyEfCIHPp1rUlC6w" keySequence="CTRL+ARROW_DOWN" command="_wYbXAHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg2nNyEfCIHPp1rUlC6w" keySequence="CTRL+ARROW_LEFT" command="_wYbVPHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg23NyEfCIHPp1rUlC6w" keySequence="CTRL+ARROW_RIGHT" command="_wYbUMHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg3HNyEfCIHPp1rUlC6w" keySequence="CTRL+HOME" command="_wYbTpnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg3XNyEfCIHPp1rUlC6w" keySequence="CTRL+NUMPAD_MULTIPLY" command="_wYbV8nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg3nNyEfCIHPp1rUlC6w" keySequence="CTRL+NUMPAD_ADD" command="_wYbWuHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg33NyEfCIHPp1rUlC6w" keySequence="CTRL+NUMPAD_SUBTRACT" command="_wYbWXXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg4HNyEfCIHPp1rUlC6w" keySequence="CTRL+NUMPAD_DIVIDE" command="_wYbT_nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg4XNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_wYbV-3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg4nNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_wYbVhnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg43NyEfCIHPp1rUlC6w" keySequence="SHIFT+CR" command="_wYbWl3NyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZg5HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_wYaFZnNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZg5XNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+SHIFT+C" command="_wYbXA3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg5nNyEfCIHPp1rUlC6w" keySequence="CTRL+TAB" command="_wYbW_HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg53NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+P" command="_wYbUtHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg6HNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+T" command="_wYbUaHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg6XNyEfCIHPp1rUlC6w" keySequence="CTRL+7" command="_wYbTqnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg6nNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+H" command="_wYbVmXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg63NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+N" command="_wYbUL3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg7HNyEfCIHPp1rUlC6w" keySequence="CTRL+/" command="_wYbTqnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg7XNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+O" command="_wYbUqHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg7nNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+A" command="_wYbWYXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg73NyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+S" command="_wYbW3nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg8HNyEfCIHPp1rUlC6w" keySequence="CTRL+#" command="_wYbWEXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg8XNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+C" command="_wYbTqnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg8nNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F" command="_wYbW_XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg83NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+G" command="_wYbTdXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg9HNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+H" command="_wYbUT3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg9XNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+I" command="_wYbVbnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg9nNyEfCIHPp1rUlC6w" keySequence="CTRL+T" command="_wYbV4HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg93NyEfCIHPp1rUlC6w" keySequence="CTRL+I" command="_wYbUIXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg-HNyEfCIHPp1rUlC6w" keySequence="CTRL+O" command="_wYbVU3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg-XNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+/" command="_wYbWtXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg-nNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+R" command="_wYbWNnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg-3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+S" command="_wYbTzXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg_HNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+T" command="_wYbWF3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg_XNyEfCIHPp1rUlC6w" keySequence="CTRL+G" command="_wYbW1nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg_nNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+L" command="_wYbU9HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZg_3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+M" command="_wYbTnXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhAHNyEfCIHPp1rUlC6w" keySequence="CTRL+=" command="_wYbWEXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhAXNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+O" command="_wYbUnHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhAnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Z" command="_wYbWTnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhA3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+\" command="_wYbVanNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhBHNyEfCIHPp1rUlC6w" keySequence="F3" command="_wYbXCHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhBXNyEfCIHPp1rUlC6w" keySequence="F4" command="_wYbWsHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhBnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+ARROW_UP" command="_wYbWPHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhB3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_wYbV0nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhCHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_UP" command="_wYbUxXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhCXNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_wYbW_nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhCnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_wYbWK3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhC3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_wYbWiHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhDHNyEfCIHPp1rUlC6w" keySequence="ALT+C" command="_wYbVa3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhDXNyEfCIHPp1rUlC6w" keySequence="SHIFT+TAB" command="_wYbVmnNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhDnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_wYaFc3NyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhD3NyEfCIHPp1rUlC6w" keySequence="CTRL+CR" command="_wYbUenNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhEHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+C" command="_wYbVKnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhEXNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+R" command="_wYbU2nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhEnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+U" command="_wYbV6XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhE3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+I" command="_wYbU1HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhFHNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_UP" command="_wYbVvXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhFXNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_DOWN" command="_wYbUf3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhFnNyEfCIHPp1rUlC6w" keySequence="SHIFT+INSERT" command="_wYbT7nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhF3NyEfCIHPp1rUlC6w" keySequence="INSERT" command="_wYbUz3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhGHNyEfCIHPp1rUlC6w" keySequence="F4" command="_wYbTx3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhGXNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_UP" command="_wYbWGXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhGnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_wYbU1nNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhG3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.contexts.window" bindingContext="_wYZiQ3NyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhHHNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+SHIFT+T" command="_wYbTynNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhHXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+SHIFT+L" command="_wYbVdnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhHnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q O" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhH3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_wYZhIHNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+B" command="_wYbVyXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhIXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+R" command="_wYbXBXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhInNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q Q" command="_wYbVvnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhI3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+S" command="_wYbVp3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhJHNyEfCIHPp1rUlC6w" keySequence="CTRL+3" command="_wYbUVXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhJXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+T" command="_wYbUoHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhJnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q S" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhJ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_wYZhKHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q V" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhKXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_wYZhKnNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+G" command="_wYbVtXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhK3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+W" command="_wYbUlXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhLHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q H" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhLXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_wYZhLnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+K" command="_wYbT9nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhL3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q K" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhMHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_wYZhMXNyEfCIHPp1rUlC6w" keySequence="CTRL+," command="_wYbTq3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhMnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q L" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhM3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_wYZhNHNyEfCIHPp1rUlC6w" keySequence="CTRL+." command="_wYbWzXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhNXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+P" command="_wYbUwHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhNnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+B" command="_wYbT93NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhN3NyEfCIHPp1rUlC6w" keySequence="CTRL+#" command="_wYbTzHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhOHNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+T" command="_wYbVPnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhOXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+E" command="_wYbUCXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhOnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q X" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhO3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_wYZhPHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q Y" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhPXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_wYZhPnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q Z" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhP3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_wYZhQHNyEfCIHPp1rUlC6w" keySequence="CTRL+P" command="_wYbWVnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhQXNyEfCIHPp1rUlC6w" keySequence="CTRL+Q" command="_wYbWZnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhQnNyEfCIHPp1rUlC6w" keySequence="CTRL+S" command="_wYbU1XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhQ3NyEfCIHPp1rUlC6w" keySequence="CTRL+W" command="_wYbVE3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhRHNyEfCIHPp1rUlC6w" keySequence="CTRL+H" command="_wYbWKXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhRXNyEfCIHPp1rUlC6w" keySequence="CTRL+K" command="_wYbVzHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhRnNyEfCIHPp1rUlC6w" keySequence="CTRL+M" command="_wYbWJHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhR3NyEfCIHPp1rUlC6w" keySequence="CTRL+N" command="_wYbW4HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhSHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+?" command="_wYbUaXNyEfCIHPp1rUlC6w">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_wYZhSXNyEfCIHPp1rUlC6w" keySequence="CTRL+B" command="_wYbTsHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhSnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q B" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhS3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_wYZhTHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+Q C" command="_wYbVvnNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhTXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_wYZhTnNyEfCIHPp1rUlC6w" keySequence="CTRL+E" command="_wYbUhXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhT3NyEfCIHPp1rUlC6w" keySequence="CTRL+F" command="_wYbT43NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhUHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+W" command="_wYbWyXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhUXNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+H" command="_wYbUfHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhUnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+N" command="_wYbUknNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhU3NyEfCIHPp1rUlC6w" keySequence="CTRL+_" command="_wYbUbXNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhVHNyEfCIHPp1rUlC6w" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_wYZhVXNyEfCIHPp1rUlC6w" keySequence="CTRL+{" command="_wYbUbXNyEfCIHPp1rUlC6w">
      <parameters xmi:id="_wYZhVnNyEfCIHPp1rUlC6w" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_wYZhV3NyEfCIHPp1rUlC6w" keySequence="SHIFT+F9" command="_wYbUp3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhWHNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_LEFT" command="_wYbTz3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhWXNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_RIGHT" command="_wYbUqnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhWnNyEfCIHPp1rUlC6w" keySequence="SHIFT+F5" command="_wYbU-nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhW3NyEfCIHPp1rUlC6w" keySequence="ALT+F7" command="_wYbVaXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhXHNyEfCIHPp1rUlC6w" keySequence="F9" command="_wYbUt3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhXXNyEfCIHPp1rUlC6w" keySequence="F11" command="_wYbWqXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhXnNyEfCIHPp1rUlC6w" keySequence="F12" command="_wYbWLXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhX3NyEfCIHPp1rUlC6w" keySequence="F2" command="_wYbTr3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhYHNyEfCIHPp1rUlC6w" keySequence="F5" command="_wYbUsXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhYXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F7" command="_wYbWrXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhYnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F8" command="_wYbUbHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhY3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F9" command="_wYbU5HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhZHNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+ARROW_LEFT" command="_wYbWZnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhZXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+ARROW_RIGHT" command="_wYbUAXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhZnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F12" command="_wYbTkHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhZ3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F4" command="_wYbUlXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhaHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F6" command="_wYbVrXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhaXNyEfCIHPp1rUlC6w" keySequence="CTRL+F7" command="_wYbVQ3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhanNyEfCIHPp1rUlC6w" keySequence="CTRL+F8" command="_wYbUTXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZha3NyEfCIHPp1rUlC6w" keySequence="CTRL+F9" command="_wYbUDHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhbHNyEfCIHPp1rUlC6w" keySequence="CTRL+F11" command="_wYbWe3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhbXNyEfCIHPp1rUlC6w" keySequence="CTRL+F12" command="_wYbT-XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhbnNyEfCIHPp1rUlC6w" keySequence="CTRL+F4" command="_wYbVE3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhb3NyEfCIHPp1rUlC6w" keySequence="CTRL+F6" command="_wYbT7HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhcHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+F7" command="_wYbV33NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhcXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_wYbVlHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhcnNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_wYbW7XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhc3NyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_wYbVgXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhdHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_wYbVpnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhdXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_wYbUcHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhdnNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+SHIFT+F12" command="_wYbWunNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhd3NyEfCIHPp1rUlC6w" keySequence="DEL" command="_wYbT8XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZheHNyEfCIHPp1rUlC6w" keySequence="ALT+?" command="_wYbUaXNyEfCIHPp1rUlC6w">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_wYZheXNyEfCIHPp1rUlC6w" keySequence="ALT+-" command="_wYbVVnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhenNyEfCIHPp1rUlC6w" keySequence="ALT+CR" command="_wYbWFHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhe3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_wYaFXnNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhfHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+P" command="_wYbUzHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhfXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+G" command="_wYbWd3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhfnNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+H" command="_wYbWNHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhf3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+R" command="_wYbTr3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhgHNyEfCIHPp1rUlC6w" keySequence="F3" command="_wYbWZ3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhgXNyEfCIHPp1rUlC6w" keySequence="F4" command="_wYbTeHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhgnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_UP" command="_wYbVZXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhg3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_wYbVZ3NyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhhHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_wYaFUHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhhXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+P" command="_wYbVYnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhhnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+A" command="_wYbXAXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhh3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+C" command="_wYbWknNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhiHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+F" command="_wYbW3HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhiXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+>" command="_wYbWRXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhinNyEfCIHPp1rUlC6w" keySequence="CTRL+I" command="_wYbWWHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhi3NyEfCIHPp1rUlC6w" keySequence="CTRL+O" command="_wYbVcHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhjHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+/" command="_wYbVVXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhjXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+\" command="_wYbVtnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhjnNyEfCIHPp1rUlC6w" keySequence="F3" command="_wYbVX3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhj3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+ARROW_UP" command="_wYbUIHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhkHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_wYbVAnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhkXNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_UP" command="_wYbV1nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhknNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_wYbV7XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhk3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_wYbTwHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhlHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_wYbVm3NyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhlXNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.compareEditorScope" bindingContext="_wYaFZ3NyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhlnNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+C" command="_wYbTvXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhl3NyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+P" command="_wYbV83NyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhmHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_wYaFaXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhmXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+T" command="_wYbUaHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhmnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+H" command="_wYbVmXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhm3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+G" command="_wYbTdXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhnHNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+H" command="_wYbUT3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhnXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+I" command="_wYbVbnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhnnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+R" command="_wYbWNnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhn3NyEfCIHPp1rUlC6w" keySequence="CTRL+G" command="_wYbW1nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhoHNyEfCIHPp1rUlC6w" keySequence="F3" command="_wYbXCHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhoXNyEfCIHPp1rUlC6w" keySequence="F4" command="_wYbWsHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhonNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_wYZiRXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZho3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+V" command="_wYbUynNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhpHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+C" command="_wYbWCnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhpXNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_UP" command="_wYbTenNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhpnNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_RIGHT" command="_wYbWp3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhp3NyEfCIHPp1rUlC6w" keySequence="SHIFT+INSERT" command="_wYbUynNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhqHNyEfCIHPp1rUlC6w" keySequence="CTRL+INSERT" command="_wYbWCnNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhqXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_wYaFYHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhqnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+M" command="_wYbTmnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhq3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+C" command="_wYbVKnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhrHNyEfCIHPp1rUlC6w" keySequence="CTRL+O" command="_wYbWwXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhrXNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+R" command="_wYbU2nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhrnNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+S" command="_wYbUm3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhr3NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+U" command="_wYbV6XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhsHNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+I" command="_wYbU1HNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhsXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_wYaFYXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhsnNyEfCIHPp1rUlC6w" keySequence="CTRL+/" command="_wYbVxHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhs3NyEfCIHPp1rUlC6w" keySequence="F3" command="_wYbVoHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhtHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.rpm.ui.specEditorScope" bindingContext="_wYaFX3NyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhtXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+O" command="_wYbWMHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhtnNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+C" command="_wYbTxnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZht3NyEfCIHPp1rUlC6w" keySequence="CTRL+O" command="_wYbUEHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhuHNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+R D" command="_wYbWlnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhuXNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+R P" command="_wYbVc3NyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhunNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_wYaFZHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhu3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+O" command="_wYbToXNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhvHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_wYaFaHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhvXNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+M" command="_wYbU6HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhvnNyEfCIHPp1rUlC6w" keySequence="ALT+CTRL+N" command="_wYbWu3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhv3NyEfCIHPp1rUlC6w" keySequence="CTRL+T" command="_wYbUWXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhwHNyEfCIHPp1rUlC6w" keySequence="CTRL+W" command="_wYbVjHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhwXNyEfCIHPp1rUlC6w" keySequence="CTRL+N" command="_wYbVsXNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhwnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.debugging" bindingContext="_wYaFbXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhw3NyEfCIHPp1rUlC6w" keySequence="CTRL+R" command="_wYbVSHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhxHNyEfCIHPp1rUlC6w" keySequence="F7" command="_wYbW0nNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhxXNyEfCIHPp1rUlC6w" keySequence="F8" command="_wYbVf3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhxnNyEfCIHPp1rUlC6w" keySequence="F5" command="_wYbTvHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhx3NyEfCIHPp1rUlC6w" keySequence="F6" command="_wYbU3HNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhyHNyEfCIHPp1rUlC6w" keySequence="CTRL+F2" command="_wYbWMXNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhyXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_wYaFbnNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhynNyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+," command="_wYbWaXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhy3NyEfCIHPp1rUlC6w" keySequence="CTRL+SHIFT+." command="_wYbWIXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhzHNyEfCIHPp1rUlC6w" keySequence="CTRL+G" command="_wYbWInNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZhzXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.editor.scope" bindingContext="_wYaFXHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZhznNyEfCIHPp1rUlC6w" keySequence="CTRL+O" command="_wYbVqXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZhz3NyEfCIHPp1rUlC6w" keySequence="F2" command="_wYbUiHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh0HNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_wYZiSHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh0XNyEfCIHPp1rUlC6w" keySequence="CTRL+O" command="_wYbU_nNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh0nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_wYaFY3NyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh03NyEfCIHPp1rUlC6w" keySequence="CTRL+O" command="_wYbToXNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh1HNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_wYaFdHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh1XNyEfCIHPp1rUlC6w" keySequence="CTRL+C" command="_wYbUXnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh1nNyEfCIHPp1rUlC6w" keySequence="CTRL+ARROW_LEFT" command="_wYbT13NyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh13NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_wYaFcXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh2HNyEfCIHPp1rUlC6w" keySequence="CTRL+C" command="_wYbT7XNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh2XNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" bindingContext="_wYaFenNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh2nNyEfCIHPp1rUlC6w" keySequence="CTRL+D" command="_wYbVZHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh23NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_wYaFb3NyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh3HNyEfCIHPp1rUlC6w" keySequence="CTRL+G" command="_wYbW3XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh3XNyEfCIHPp1rUlC6w" keySequence="HOME" command="_wYbUWHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh3nNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.console" bindingContext="_wYaFanNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh33NyEfCIHPp1rUlC6w" keySequence="CTRL+Z" command="_wYbWxHNyEfCIHPp1rUlC6w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_wYZh4HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_wYaFcHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh4XNyEfCIHPp1rUlC6w" keySequence="SHIFT+F7" command="_wYbVn3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh4nNyEfCIHPp1rUlC6w" keySequence="SHIFT+F8" command="_wYbWH3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh43NyEfCIHPp1rUlC6w" keySequence="SHIFT+F5" command="_wYbU3XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh5HNyEfCIHPp1rUlC6w" keySequence="SHIFT+F6" command="_wYbTs3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh5XNyEfCIHPp1rUlC6w" keySequence="CTRL+F5" command="_wYbWoXNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh5nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_wYaFd3NyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh53NyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_LEFT" command="_wYbV4XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh6HNyEfCIHPp1rUlC6w" keySequence="ALT+ARROW_RIGHT" command="_wYbV93NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh6XNyEfCIHPp1rUlC6w" keySequence="F3" command="_wYbXCHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh6nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_wYaFYnNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh63NyEfCIHPp1rUlC6w" keySequence="F1" command="_wYbThHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh7HNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_wYaFdXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh7XNyEfCIHPp1rUlC6w" keySequence="F2" command="_wYbT9HNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh7nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_wYaFXXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh73NyEfCIHPp1rUlC6w" keySequence="F3" command="_wYbXCHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh8HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_wYaFfHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh8XNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_UP" command="_wYbUq3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh8nNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_wYbTmHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh83NyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_wYbVLXNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh9HNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_wYbVj3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh9XNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+HOME" command="_wYbWx3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh9nNyEfCIHPp1rUlC6w" keySequence="ALT+SHIFT+END" command="_wYbUhHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh93NyEfCIHPp1rUlC6w" keySequence="ALT+R" command="_wYbWKHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh-HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_wYaFbHNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh-XNyEfCIHPp1rUlC6w" keySequence="CTRL+INSERT" command="_wYbVpHNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh-nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" bindingContext="_wYaFZXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh-3NyEfCIHPp1rUlC6w" keySequence="ESC CTRL+F" command="_wYbVenNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZh_HNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.view.context" bindingContext="_wYaFeXNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZh_XNyEfCIHPp1rUlC6w" keySequence="Z" command="_wYbU9XNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh_nNyEfCIHPp1rUlC6w" keySequence="+" command="_wYbTdnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZh_3NyEfCIHPp1rUlC6w" keySequence="-" command="_wYbTwnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiAHNyEfCIHPp1rUlC6w" keySequence="/" command="_wYbUJ3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiAXNyEfCIHPp1rUlC6w" keySequence="S" command="_wYbUmHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiAnNyEfCIHPp1rUlC6w" keySequence="W" command="_wYbVBHNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiA3NyEfCIHPp1rUlC6w" keySequence="A" command="_wYbUW3NyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiBHNyEfCIHPp1rUlC6w" keySequence="D" command="_wYbVCnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiBXNyEfCIHPp1rUlC6w" keySequence="=" command="_wYbTdnNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZiBnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_wYaFcnNyEfCIHPp1rUlC6w">
    <bindings xmi:id="_wYZiB3NyEfCIHPp1rUlC6w" keySequence="ALT+Y" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiCHNyEfCIHPp1rUlC6w" keySequence="ALT+A" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiCXNyEfCIHPp1rUlC6w" keySequence="ALT+B" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiCnNyEfCIHPp1rUlC6w" keySequence="ALT+C" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiC3NyEfCIHPp1rUlC6w" keySequence="ALT+D" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiDHNyEfCIHPp1rUlC6w" keySequence="ALT+E" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiDXNyEfCIHPp1rUlC6w" keySequence="ALT+F" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiDnNyEfCIHPp1rUlC6w" keySequence="ALT+G" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiD3NyEfCIHPp1rUlC6w" keySequence="ALT+P" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiEHNyEfCIHPp1rUlC6w" keySequence="ALT+R" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiEXNyEfCIHPp1rUlC6w" keySequence="ALT+S" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiEnNyEfCIHPp1rUlC6w" keySequence="ALT+T" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiE3NyEfCIHPp1rUlC6w" keySequence="ALT+V" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiFHNyEfCIHPp1rUlC6w" keySequence="ALT+W" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiFXNyEfCIHPp1rUlC6w" keySequence="ALT+H" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiFnNyEfCIHPp1rUlC6w" keySequence="ALT+L" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
    <bindings xmi:id="_wYZiF3NyEfCIHPp1rUlC6w" keySequence="ALT+N" command="_wYbVUnNyEfCIHPp1rUlC6w"/>
  </bindingTables>
  <bindingTables xmi:id="_wYZiGHNyEfCIHPp1rUlC6w" bindingContext="_wYaFfXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiGXNyEfCIHPp1rUlC6w" bindingContext="_wYaFfnNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiGnNyEfCIHPp1rUlC6w" bindingContext="_wYaFf3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiG3NyEfCIHPp1rUlC6w" bindingContext="_wYaFgHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiHHNyEfCIHPp1rUlC6w" bindingContext="_wYaFgXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiHXNyEfCIHPp1rUlC6w" bindingContext="_wYaFgnNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiHnNyEfCIHPp1rUlC6w" bindingContext="_wYaFg3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiH3NyEfCIHPp1rUlC6w" bindingContext="_wYaFhHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiIHNyEfCIHPp1rUlC6w" bindingContext="_wYaFhXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiIXNyEfCIHPp1rUlC6w" bindingContext="_wYaFhnNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiInNyEfCIHPp1rUlC6w" bindingContext="_wYaFh3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiI3NyEfCIHPp1rUlC6w" bindingContext="_wYaFiHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiJHNyEfCIHPp1rUlC6w" bindingContext="_wYaFiXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiJXNyEfCIHPp1rUlC6w" bindingContext="_wYaFinNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiJnNyEfCIHPp1rUlC6w" bindingContext="_wYaFi3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiJ3NyEfCIHPp1rUlC6w" bindingContext="_wYaFjHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiKHNyEfCIHPp1rUlC6w" bindingContext="_wYaFjXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiKXNyEfCIHPp1rUlC6w" bindingContext="_wYaFjnNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiKnNyEfCIHPp1rUlC6w" bindingContext="_wYaFj3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiK3NyEfCIHPp1rUlC6w" bindingContext="_wYaFkHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiLHNyEfCIHPp1rUlC6w" bindingContext="_wYaFkXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiLXNyEfCIHPp1rUlC6w" bindingContext="_wYaFknNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiLnNyEfCIHPp1rUlC6w" bindingContext="_wYaFk3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiL3NyEfCIHPp1rUlC6w" bindingContext="_wYaFlHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiMHNyEfCIHPp1rUlC6w" bindingContext="_wYaFlXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiMXNyEfCIHPp1rUlC6w" bindingContext="_wYaFlnNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiMnNyEfCIHPp1rUlC6w" bindingContext="_wYaFl3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiM3NyEfCIHPp1rUlC6w" bindingContext="_wYaFmHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiNHNyEfCIHPp1rUlC6w" bindingContext="_wYaFmXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiNXNyEfCIHPp1rUlC6w" bindingContext="_wYaFmnNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiNnNyEfCIHPp1rUlC6w" bindingContext="_wYaFm3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiN3NyEfCIHPp1rUlC6w" bindingContext="_wYaFnHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiOHNyEfCIHPp1rUlC6w" bindingContext="_wYaFnXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiOXNyEfCIHPp1rUlC6w" bindingContext="_wYaFnnNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiOnNyEfCIHPp1rUlC6w" bindingContext="_wYaFn3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiO3NyEfCIHPp1rUlC6w" bindingContext="_wYaFoHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiPHNyEfCIHPp1rUlC6w" bindingContext="_wYaFoXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiPXNyEfCIHPp1rUlC6w" bindingContext="_wYaFonNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiPnNyEfCIHPp1rUlC6w" bindingContext="_wYaFo3NyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiP3NyEfCIHPp1rUlC6w" bindingContext="_wYaFpHNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiQHNyEfCIHPp1rUlC6w" bindingContext="_wYaFpXNyEfCIHPp1rUlC6w"/>
  <bindingTables xmi:id="_wYZiQXNyEfCIHPp1rUlC6w" bindingContext="_wYaFpnNyEfCIHPp1rUlC6w"/>
  <rootContext xmi:id="_wYZiQnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_wYZiQ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_wYZiRHNyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_wYZiRXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_wYZiRnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_wYZiR3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_wYZiSHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_wYaFUHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_wYaFUXNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_wYaFUnNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_wYaFU3NyEfCIHPp1rUlC6w" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_wYaFVHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_wYaFVXNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_wYaFVnNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_wYaFV3NyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_wYaFWHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_wYaFWXNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_wYaFWnNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_wYaFW3NyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
        </children>
        <children xmi:id="_wYaFXHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.editor.scope" name="Autoconf Editor" description="Editor for Autoconf Configuration Source Files"/>
        <children xmi:id="_wYaFXXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_wYaFXnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_wYaFX3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.rpm.ui.specEditorScope" name="Specfile Editor Context"/>
        <children xmi:id="_wYaFYHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_wYaFYXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_wYaFYnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_wYaFY3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_wYaFZHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_wYaFZXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" name="ChangeLog Editor"/>
        <children xmi:id="_wYaFZnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_wYaFZ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_wYaFaHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_wYaFaXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_wYaFanNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_wYaFa3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_wYaFbHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_wYaFbXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_wYaFbnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_wYaFb3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_wYaFcHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_wYaFcXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_wYaFcnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_wYaFc3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_wYaFdHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_wYaFdXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_wYaFdnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_wYaFd3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_wYaFeHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_wYaFeXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.view.context" name="In Time-Based View"/>
  <rootContext xmi:id="_wYaFenNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" name="In Time Graph"/>
  <rootContext xmi:id="_wYaFe3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_wYaFfHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_wYaFfXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_wYaFfnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_wYaFf3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_wYaFgHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_wYaFgXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_wYaFgnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_wYaFg3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_wYaFhHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.cdt.ui.actionSet" name="Auto::org.eclipse.mylyn.cdt.ui.actionSet"/>
  <rootContext xmi:id="_wYaFhXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_wYaFhnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_wYaFh3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_wYaFiHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_wYaFiXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_wYaFinNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_wYaFi3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_wYaFjHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_wYaFjXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_wYaFjnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_wYaFj3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_wYaFkHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_wYaFkXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_wYaFknNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_wYaFk3NyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset" name="Auto::org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset"/>
  <rootContext xmi:id="_wYaFlHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_wYaFlXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_wYaFlnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_wYaFl3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_wYaFmHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_wYaFmXNyEfCIHPp1rUlC6w" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_wYaFmnNyEfCIHPp1rUlC6w" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_wYaFm3NyEfCIHPp1rUlC6w" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_wYaFnHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_wYaFnXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_wYaFnnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_wYaFn3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_wYaFoHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_wYaFoXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_wYaFonNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_wYaFo3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_wYaFpHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_wYaFpXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_wYaFpnNyEfCIHPp1rUlC6w" elementId="ilg.gnumcueclipse.debug.gdbjtag.jlink.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::ilg.gnumcueclipse.debug.gdbjtag.jlink.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_wYaFp3NyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFqHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFqXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFqnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFq3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFrHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFrXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFrnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFr3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFsHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFsXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFsnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.testsrunner.resultsview" label="C/C++ Unit" iconURI="platform:/plugin/org.eclipse.cdt.testsrunner/icons/eview16/cppunit.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.testsrunner.internal.ui.view.ResultsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.testsrunner"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFs3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFtHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFtXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFtnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFt3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFuHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFuXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.svg" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFunNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFu3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFvHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFvXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFvnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFv3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFwHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.svg" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFwXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFwnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFw3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFxHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFxXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFxnNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFx3NyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFyHNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/hardware_chip.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFyXNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/board.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFynNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/info_obj.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFy3NyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.packs.ui.views.PacksView" label="CMSIS Packs" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/packages.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.PacksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFzHNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/outline_co.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFzXNyEfCIHPp1rUlC6w" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.svg" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFznNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.dataviewers.charts.view.ChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.dataviewers.charts"/>
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_wYaFz3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF0HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF0XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF0nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF03NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.gcov.view" label="gcov" iconURI="platform:/plugin/org.eclipse.linuxtools.gcov.core/icons/toggle.gif" tooltip="" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gcov.view.CovView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gcov.core"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF1HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gprof.view.GmonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gprof"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF1XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.valgrind.ui.valgrindview" label="Valgrind" iconURI="platform:/plugin/org.eclipse.linuxtools.valgrind.ui/icons/valgrind-icon.png" tooltip="" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.valgrind.ui.ValgrindViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.valgrind.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF1nNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF13NyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF2HNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF2XNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF2nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF23NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF3HNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF3XNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.svg" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF3nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.svg" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF33NyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF4HNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.presentation.NotificationView" label="Notification" iconURI="platform:/plugin/org.eclipse.oomph.setup.editor/icons/notification.png" tooltip="" allowMultiple="true" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.setup.presentation.NotificationViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.setup.editor"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF4XNyEfCIHPp1rUlC6w" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.svg" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF4nNyEfCIHPp1rUlC6w" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF43NyEfCIHPp1rUlC6w" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF5HNyEfCIHPp1rUlC6w" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.svg" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF5XNyEfCIHPp1rUlC6w" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.svg" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF5nNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF53NyEfCIHPp1rUlC6w" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF6HNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.counters.ui.views.countersview" label="Counters" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.counters.ui/icons/counter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.counters.ui.views.CounterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.counters.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF6XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.analysis.graph.ui.criticalpath.view.criticalpathview" label="Critical Flow View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.graph.ui/icons/eview16/critical-path.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.graph.ui.criticalpath.view.CriticalPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.graph.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF6nNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.lami.views.reportview" label="Analysis Report" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.provisional.analysis.lami.ui.views.LamiReportView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.lami.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF63NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF7HNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.scatter" label="Sched_Wakeup/Switch Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF7XNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF7nNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname" label="Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF73NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF8HNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority" label="Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF8XNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF8nNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.density" label="Sched_Wakeup/Switch Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF83NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.controlflow.ControlFlowView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF9HNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.resources.ResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF9XNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/cpu-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.cpuusage.CpuUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF9nNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF93NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.scatter" label="System Call Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF-HNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF-XNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.density" label="System Call Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF-nNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.kernelmemoryusageview" label="Kernel Memory Usage View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.kernelmemoryusage.KernelMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF-3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.diskioactivity" label="Disk I/O Activity" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.io.diskioactivity.DiskIOActivityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF_HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Flame Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.profiling.ui.views.flamechart.FlameChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF_XNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.callgraphDensity" label="Function Durations Distribution" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/funcdensity.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.CallGraphDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF_nNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.flamegraph.flamegraphView" label="Flame Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph.FlameGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaF_3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.statistics.callgraphstatistics" label="Function Duration Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.statistics.CallGraphStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGAHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.profiling.ui.flamegraph" label="Flame Graph (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/elcl16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph2.FlameGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGAXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.profiling.ui.flamegraph.selection" label="Flame Graph Selection (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/elcl16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph2.FlameGraphSelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGAnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.profiling.ui.functiondensity" label="Function Durations Distribution (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/elcl16/funcdensity.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.functiondensity.FunctionDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGA3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.profiling.ui.weightedtree" label="Weighted Tree Viewer (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/obj16/stckframe_obj.gif" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.weightedtree.WeightedTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGBHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.profiling.ui.flamechart" label="Flame Chart (new Callstack)" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/obj16/stckframe_obj.gif" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.FlameChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGBXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table" label="Segment Store Table" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGBnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics" label="Descriptive Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGB3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2" label="Segments vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGCHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGCXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGCnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Matches Scatter Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGC3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.control.ui.views.ControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.control.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGDHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.ust.ui.views.memusage.UstMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGDXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGDnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGD3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/ganttxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.timegraph.XmlTimeGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGEHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/linechartxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.xychart.XmlXYView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGEXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latencytable" label="Latency Table" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternLatencyTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGEnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.scattergraph" label="Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/scatter.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternScatterGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGE3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.density" label="Latency vs Count" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/density.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGFHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.statistics" label="Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGFXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.timechart.TimeChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGFnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statesystem.TmfStateSystemExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGF3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.colors.ColorsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGGHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.filter.FilterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGGXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.uml2sd.SDView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGGnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.statistics.TmfStatisticsViewImpl"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGG3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.histogram.HistogramView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGHHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.views.eventdensity" label="Event Density" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.eventdensity.EventDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGHXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.synchronization.TmfSynchronizationView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGHnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGH3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGIHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.svg" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGIXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGInNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGI3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGJHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGJXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGJnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGJ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGKHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGKXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGKnNyEfCIHPp1rUlC6w" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGK3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGLHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_wYaGLXNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <trimContributions xmi:id="_wYav7nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_wYav73NyEfCIHPp1rUlC6w" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_wYav8HNyEfCIHPp1rUlC6w" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_wYav8XNyEfCIHPp1rUlC6w" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_wYbTcHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTcXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTcnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbTc3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_wYbTdHNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_wYbXjnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTdXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTdnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin.selection" commandName="Zoom in (selection)" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTd3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTeHNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTeXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTenNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_wYbXqHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTe3NyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTfHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTfXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTfnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTf3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTgHNyEfCIHPp1rUlC6w" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbTgXNyEfCIHPp1rUlC6w" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_wYbTgnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTg3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbThHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbThXNyEfCIHPp1rUlC6w" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_wYbXrXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbThnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTh3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTiHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_wYbXmHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTiXNyEfCIHPp1rUlC6w" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTinNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.OpenNewViewCommand" commandName="Open New View" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTi3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.application.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_wYbXp3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTjHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTjXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_wYbXp3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTjnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTj3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTkHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTkXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTknNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.application.command.debugRemoteExecutable" commandName="Debug Remote Executable" description="Debug a Remote executable" category="_wYbXp3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTk3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_wYbXkXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTlHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTlXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableLogger" commandName="Disable Logger" description="Disable Logger" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTlnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTl3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTmHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_wYbXrHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTmXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.analysis.xml.ui.managexmlanalyses" commandName="Manage XML analyses..." description="Manage XML files containing analysis information" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTmnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_wYbXmnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTm3NyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTnHNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTnXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_wYbXh3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTnnNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTn3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbToHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbToXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTonNyEfCIHPp1rUlC6w" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_wYbXgHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTo3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTpHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTpXNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTpnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTp3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTqHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTqXNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTqnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTq3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTrHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTrXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.open_as_experiment" commandName="Open As Experiment..." description="Open selected traces as an experiment" category="_wYbXg3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbTrnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_wYbTr3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTsHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTsXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTsnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.valgrind.launch.clearMarkersCommand" commandName="Remove Valgrind Markers" description="Removes all Valgrind markers" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTs3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_wYbXsHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTtHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.save" commandName="Save..." description="Save session(s)" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTtXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTtnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.application.command.debugAttachedExecutable" commandName="Debug Attached Executable" description="Debug an attached executable" category="_wYbXp3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTt3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTuHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTuXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTunNyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_wYbXrnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTu3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTvHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTvXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.changelog.core.actions.KeyActionCommand" commandName="Insert ChangeLog entry" description="Insert a ChangeLog entry" category="_wYbXfnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTvnNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTv3NyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTwHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTwXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTwnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout.selection" commandName="Zoom out (selection)" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTw3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTxHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbTxXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_wYbTxnNyEfCIHPp1rUlC6w" elementId="rpmEditor.toggleComment.command" commandName="Toggle Comment" category="_wYbXpXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTx3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTyHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTyXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTynNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_wYbXoXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTy3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTzHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTzXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTznNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbTz3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT0HNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT0XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT0nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT03NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT1HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT1XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT1nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_wYbXenNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT13NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT2HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.changelog.core.prepareCommit" commandName="Prepare Commit" description="Copies latest changelog entry to clipboard" category="_wYbXfnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT2XNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT2nNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.packs.ui.commands.updateCommand" commandName="Refresh" category="_wYbXiHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT23NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT3HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT3XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT3nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.remove" commandName="Remove" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT33NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT4HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.gdbtrace.ui.command.project.trace.selectexecutable" commandName="Select Trace Executable..." description="Select executable binary file for a GDB trace" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT4XNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT4nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT43NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT5HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT5XNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT5nNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbT53NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_wYbT6HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT6XNyEfCIHPp1rUlC6w" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT6nNyEfCIHPp1rUlC6w" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_wYbXlnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT63NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT7HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT7XNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT7nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT73NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT8HNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT8XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT8nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT83NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT9HNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT9XNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT9nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT93NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT-HNyEfCIHPp1rUlC6w" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_wYbXqnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT-XNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT-nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT-3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT_HNyEfCIHPp1rUlC6w" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_wYbXr3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT_XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT_nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbT_3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUAHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUAXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUAnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUA3NyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_wYbXknNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUBHNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUBXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUBnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_wYbXg3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUB3NyEfCIHPp1rUlC6w" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_wYbUCHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUCXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUCnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUC3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUDHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUDXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUDnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUD3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUEHNyEfCIHPp1rUlC6w" elementId="rpmEditor.showOutline.command" commandName="Show Outline" category="_wYbXpXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUEXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUEnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUE3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUFHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUFXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUFnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUF3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_wYbXenNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUGHNyEfCIHPp1rUlC6w" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_wYbUGXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_wYbXh3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUGnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUG3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUHHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUHXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUHnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUH3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUIHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUIXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUInNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUI3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUJHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUJXNyEfCIHPp1rUlC6w" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUJnNyEfCIHPp1rUlC6w" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_wYbUJ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.filter" commandName="Filter Time Graph events" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUKHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUKXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUKnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_wYbXfHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUK3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_wYbXnnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbULHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbULXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbULnNyEfCIHPp1rUlC6w" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_wYbXqXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUL3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUMHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUMXNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUMnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.logger" commandName="Enable Logger..." description="Assign Logger to Session and Enable Logger" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUM3NyEfCIHPp1rUlC6w" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUNHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUNXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUNnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.remote.ui.command.fetchlog" commandName="Fetch Remote Traces..." category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUN3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUOHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUOXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUOnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUO3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUPHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUPXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_wYbXgXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUPnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUP3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_wYbXo3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUQHNyEfCIHPp1rUlC6w" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_wYbUQXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUQnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUQ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbURHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbURXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbURnNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUR3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUSHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_wYbXfHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUSXNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_wYbXjnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUSnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUS3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUTHNyEfCIHPp1rUlC6w" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUTXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUTnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUT3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUUHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUUXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUUnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_wYbXq3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUU3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_wYbUVHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUVXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUVnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUV3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_wYbXmnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUWHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUWXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUWnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUW3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.left" commandName="Left" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUXHNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUXXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUXnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUX3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_wYbXenNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUYHNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUYXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUYnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_wYbXhnNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUY3NyEfCIHPp1rUlC6w" elementId="url" name="URL"/>
    <parameters xmi:id="_wYbUZHNyEfCIHPp1rUlC6w" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_wYbUZXNyEfCIHPp1rUlC6w" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_wYbUZnNyEfCIHPp1rUlC6w" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_wYbUZ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUaHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUaXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUanNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUa3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.command.libtoolize" commandName="Invoke Libtoolize" description="Run libtoolize in the selected directory" category="_wYbXiXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUbHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUbXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_wYbXhnNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUbnNyEfCIHPp1rUlC6w" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_wYbUb3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.PinViewCommand" commandName="Pin to Debug Context" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUcHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUcXNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUcnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUc3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUdHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUdXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUdnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUd3NyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUeHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.command.autoconf" commandName="Invoke Autoconf" description="Run autoconf in the selected directory" category="_wYbXiXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUeXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUenNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUe3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUfHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUfXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUfnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_add" commandName="Add External Analysis" description="Add External Analysis" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUf3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUgHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUgXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_wYbXmHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUgnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUg3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUhHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_wYbXrHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUhXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUhnNyEfCIHPp1rUlC6w" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_wYbXgHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUh3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUiHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.editors.text.show.tooltip" commandName="Show Tooltip Description" description="Shows the tooltip description for the element at the cursor" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUiXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUinNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUi3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUjHNyEfCIHPp1rUlC6w" elementId="rpmlint.toggleRpmlint.command" commandName="Toggle Rpmlint" category="_wYbXhXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUjXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUjnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUj3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUkHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUkXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_wYbXlHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUknNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUk3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUlHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUlXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUlnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUl3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUmHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout" commandName="Zoom out (mouse position)" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUmXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUmnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUm3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_wYbXmnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUnHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUnXNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUnnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUn3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUoHNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUoXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUonNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_wYbXg3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUo3NyEfCIHPp1rUlC6w" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_wYbUpHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.report_delete" commandName="Delete Report" description="Delete this report from the project" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUpXNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_wYbXnXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUpnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUp3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUqHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUqXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUqnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUq3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_wYbXrHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUrHNyEfCIHPp1rUlC6w" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_wYbXqXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUrXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUrnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUr3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUsHNyEfCIHPp1rUlC6w" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_wYbXqnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUsXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUsnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUs3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUtHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUtXNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_wYbXjnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUtnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUt3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUuHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUuXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUunNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUu3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUvHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUvXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_wYbXp3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUvnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUv3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUwHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog" commandName="Prepare Changelog" description="Prepares Changelog" category="_wYbXfnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUwXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUwnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUw3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUxHNyEfCIHPp1rUlC6w" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_wYbUxXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUxnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUx3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUyHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.command.automake" commandName="Invoke Automake" description="Run automake from the selected directory" category="_wYbXiXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUyXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUynNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_wYbXqHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUy3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUzHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbUzXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_wYbXn3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbUznNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_wYbUz3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU0HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU0XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU0nNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU03NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU1HNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU1XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU1nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU13NyEfCIHPp1rUlC6w" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_wYbXlnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU2HNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.trim_trace" commandName="Export Time Selection as New Trace..." description="Create a new trace containing only the events in the currently selected time range. Only available if the trace type supports it, and if a time range is selected." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU2XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU2nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU23NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU3HNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU3XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_wYbXsHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU3nNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU33NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_wYbXg3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbU4HNyEfCIHPp1rUlC6w" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_wYbU4XNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU4nNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU43NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU5HNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU5XNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU5nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU53NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU6HNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU6XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU6nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.meson.ui.command.runninja" commandName="Run ninja" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU63NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU7HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU7XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_wYbXrnNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbU7nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_wYbU73NyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_wYbU8HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU8XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU8nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU83NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU9HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_wYbXh3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU9XNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.zoom.selection" commandName="Zoom to selection" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU9nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU93NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU-HNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU-XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU-nNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU-3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU_HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU_XNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_wYbXkHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU_nNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbU_3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVAHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_wYbXhnNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVAXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_wYbVAnNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVA3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVBHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin" commandName="Zoom in (mouse position)" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVBXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_wYbXo3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVBnNyEfCIHPp1rUlC6w" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_wYbVB3NyEfCIHPp1rUlC6w" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_wYbVCHNyEfCIHPp1rUlC6w" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_wYbVCXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVCnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.right" commandName="Right" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVC3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVDHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVDXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVDnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_wYbXh3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVD3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVEHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVEXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_wYbXpnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVEnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_wYbXoXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVE3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVFHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVFXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVFnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_wYbVF3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVGHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_wYbXi3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVGXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_wYbVGnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_wYbVG3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_wYbXn3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVHHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_wYbVHXNyEfCIHPp1rUlC6w" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVHnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVH3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVIHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVIXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_wYbXjXNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVInNyEfCIHPp1rUlC6w" elementId="title" name="Title"/>
    <parameters xmi:id="_wYbVI3NyEfCIHPp1rUlC6w" elementId="message" name="Message"/>
    <parameters xmi:id="_wYbVJHNyEfCIHPp1rUlC6w" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_wYbVJXNyEfCIHPp1rUlC6w" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_wYbVJnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.load" commandName="Load..." description="Load session(s)" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVJ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVKHNyEfCIHPp1rUlC6w" elementId="rpmlint.runRpmlint.command" commandName="Run rpmlint" category="_wYbXhXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVKXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_wYbXsXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVKnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVK3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVLHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.command.aclocal" commandName="Invoke Aclocal" description="Run aclocal from the selected directory" category="_wYbXiXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVLXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_wYbXrHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVLnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVL3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVMHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.command.autoheader" commandName="Invoke Autoheader" description="Run autoheader from the selected directory" category="_wYbXiXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVMXNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVMnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVM3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVNHNyEfCIHPp1rUlC6w" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_wYbXqXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVNXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVNnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVN3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.command.autoreconf" commandName="Invoke Autoreconf" description="Run autoreconf from the selected directory" category="_wYbXiXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVOHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVOXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVOnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVO3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVPHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVPXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_wYbXp3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVPnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_wYbXoXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVP3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_wYbXhnNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVQHNyEfCIHPp1rUlC6w" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_wYbVQXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVQnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVQ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVRHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_wYbXeHNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVRXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_wYbVRnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVR3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVSHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVSXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVSnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVS3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVTHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVTXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVTnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVT3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVUHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVUXNyEfCIHPp1rUlC6w" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVUnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_wYbXqHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVU3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVVHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVVXNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVVnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVV3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVWHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVWXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVWnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_wYbXsXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVW3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVXHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVXXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVXnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVX3NyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVYHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_wYbXenNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVYXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVYnNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVY3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVZHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.timegraph.bookmark" commandName="Toggle Bookmark..." category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVZXNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVZnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVZ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVaHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVaXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVanNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVa3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_wYbXh3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVbHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_wYbXq3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVbXNyEfCIHPp1rUlC6w" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_wYbVbnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVb3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVcHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVcXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.application.command.debugCore" commandName="Debug Core File" description="Debug a corefile" category="_wYbXp3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVcnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVc3NyEfCIHPp1rUlC6w" elementId="rpmEditor.prepareSources.command" commandName="Prepare Sources" category="_wYbXpXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVdHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVdXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVdnNyEfCIHPp1rUlC6w" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_wYbXhHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVd3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_wYbXmHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVeHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_wYbXfHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVeXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVenNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.changelog.core.formatChangeLog" commandName="Format ChangeLog" description="Formats ChangeLog" category="_wYbXfnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVe3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVfHNyEfCIHPp1rUlC6w" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVfXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVfnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_wYbXlXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVf3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVgHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVgXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVgnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVg3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVhHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVhXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVhnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVh3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.bugs.commands.ReportBugAction" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement for predefined Products / Projects" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbViHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbViXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVinNyEfCIHPp1rUlC6w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVi3NyEfCIHPp1rUlC6w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_wYbVjHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVjXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVjnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVj3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_wYbXrHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVkHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVkXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVknNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVk3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVlHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVlXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVlnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVl3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVmHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVmXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVmnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVm3NyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVnHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVnXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVnnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVn3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_wYbXsHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVoHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_wYbXinNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVoXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVonNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVo3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_wYbXrXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVpHNyEfCIHPp1rUlC6w" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_wYbXgHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVpXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVpnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVp3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVqHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVqXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.command.showOutline" commandName="Show Outline" category="_wYbXiXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVqnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVq3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVrHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVrXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVrnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVr3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVsHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVsXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVsnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVs3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVtHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVtXNyEfCIHPp1rUlC6w" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_wYbXqXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVtnNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVt3NyEfCIHPp1rUlC6w" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_wYbXr3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVuHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVuXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVunNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_wYbXsHNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVu3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_wYbVvHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVvXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVvnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_wYbXl3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVv3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_wYbVwHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_wYbVwXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_wYbVwnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_wYbXh3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVw3NyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.notifications" commandName="Notifications" description="Notifications for the Eclipse IDE" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVxHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_wYbXinNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVxXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVxnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVx3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_wYbXn3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbVyHNyEfCIHPp1rUlC6w" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_wYbVyXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVynNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVy3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVzHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVzXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVznNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.valgrind.launch.exportCommand" commandName="Export Valgrind Log Files" description="Exports Valgrind log output to a directory" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbVz3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV0HNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_wYbXf3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbV0XNyEfCIHPp1rUlC6w" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_wYbV0nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV03NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV1HNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_wYbXenNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbV1XNyEfCIHPp1rUlC6w" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_wYbV1nNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV13NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_wYbXo3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbV2HNyEfCIHPp1rUlC6w" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_wYbV2XNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV2nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV23NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV3HNyEfCIHPp1rUlC6w" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV3XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV3nNyEfCIHPp1rUlC6w" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_wYbXqnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV33NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV4HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV4XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV4nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces..." description="Synchronize 2 or more traces" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV43NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV5HNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV5XNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV5nNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV53NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV6HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_wYbXp3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV6XNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV6nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV63NyEfCIHPp1rUlC6w" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_wYbXqXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV7HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableLogger" commandName="Enable Logger" description="Enable Logger" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV7XNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV7nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV73NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_wYbXenNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV8HNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_wYbXoXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV8XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeCommand" commandName="Instruction Stepping Mode" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV8nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV83NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog2" commandName="Prepare Changelog In Editor" description="Prepares ChangeLog in an editor" category="_wYbXfnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV9HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV9XNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV9nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV93NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV-HNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV-XNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV-nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV-3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV_HNyEfCIHPp1rUlC6w" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV_XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV_nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbV_3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWAHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWAXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWAnNyEfCIHPp1rUlC6w" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_wYbXqnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWA3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWBHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWBXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWBnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWB3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWCHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWCXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWCnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_wYbXqHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWC3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWDHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWDXNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWDnNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWD3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_wYbXrnNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbWEHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_wYbWEXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWEnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWE3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWFHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWFXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWFnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWF3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_wYbXh3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWGHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWGXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWGnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.cdt.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_wYbXgnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWG3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWHHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWHXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWHnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWH3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_wYbXsHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWIHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWIXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWInNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWI3NyEfCIHPp1rUlC6w" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWJHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWJXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWJnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWJ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWKHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_wYbXrHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWKXNyEfCIHPp1rUlC6w" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_wYbXqXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWKnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWK3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWLHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWLXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWLnNyEfCIHPp1rUlC6w" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWL3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWMHNyEfCIHPp1rUlC6w" elementId="rpmEditor.organizePatches.command" commandName="Organize Patches" category="_wYbXpXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWMXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWMnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWM3NyEfCIHPp1rUlC6w" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWNHNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWNXNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWNnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_wYbXh3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWN3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_wYbXlXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWOHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWOXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWOnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWO3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWPHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWPXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_wYbXrXNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbWPnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_wYbWP3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_wYbWQHNyEfCIHPp1rUlC6w" elementId="rpmEditor.build.command" commandName="RPM Build Command" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbWQXNyEfCIHPp1rUlC6w" elementId="buildType" name="buildType" optional="false"/>
    <parameters xmi:id="_wYbWQnNyEfCIHPp1rUlC6w" elementId="actOnEditor" name="actOnEditor"/>
  </commands>
  <commands xmi:id="_wYbWQ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWRHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWRXNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWRnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWR3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWSHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWSXNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWSnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWS3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWTHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_wYbXpHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWTXNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.problem" commandName="Report a Problem" description="Report a problem for this IDE" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWTnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWT3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWUHNyEfCIHPp1rUlC6w" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWUXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWUnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWU3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWVHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWVXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_wYbXrnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWVnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_wYbXg3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWV3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWWHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWWXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWWnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWW3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWXHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWXXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWXnNyEfCIHPp1rUlC6w" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWX3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWYHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWYXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWYnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWY3NyEfCIHPp1rUlC6w" elementId="org.eclipse.launchbar.ui.command.openLaunchSelector" commandName="Open Launch Bar Config Selector" category="_wYbXqnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWZHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWZXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWZnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWZ3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWaHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWaXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWanNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWa3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWbHNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWbXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_wYbXjXNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbWbnNyEfCIHPp1rUlC6w" elementId="title" name="Title"/>
    <parameters xmi:id="_wYbWb3NyEfCIHPp1rUlC6w" elementId="message" name="Message"/>
    <parameters xmi:id="_wYbWcHNyEfCIHPp1rUlC6w" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_wYbWcXNyEfCIHPp1rUlC6w" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_wYbWcnNyEfCIHPp1rUlC6w" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_wYbWc3NyEfCIHPp1rUlC6w" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_wYbWdHNyEfCIHPp1rUlC6w" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_wYbWdXNyEfCIHPp1rUlC6w" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_wYbWdnNyEfCIHPp1rUlC6w" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_wYbWd3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWeHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWeXNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.packs.ui.commands.showPerspectiveCommand" commandName="Switch to CMSIS Packs Perspective" category="_wYbXiHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWenNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWe3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWfHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWfXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWfnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_wYbXlXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWf3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.command.reconfigure" commandName="Reconfigure Project" description="Run configuration scripts for project" category="_wYbXiXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWgHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWgXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWgnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWg3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWhHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWhXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWhnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWh3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWiHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWiXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWinNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbWi3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_wYbWjHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_wYbWjXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWjnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWj3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWkHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_wYbXlXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWkXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWknNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWk3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_wYbXsXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWlHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWlXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWlnNyEfCIHPp1rUlC6w" elementId="rpmEditor.downloadSources.command" commandName="Download Sources" category="_wYbXpXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWl3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWmHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_wYbXj3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbWmXNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_wYbWmnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWm3NyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_wYbXnHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWnHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWnXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWnnNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWn3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWoHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_wYbXenNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWoXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWonNyEfCIHPp1rUlC6w" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWo3NyEfCIHPp1rUlC6w" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_wYbXlnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWpHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWpXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWpnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWp3NyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_wYbXqHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWqHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.convert_project" commandName="Configure or convert to Tracing Project" description="Configure or convert project to tracing project" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWqXNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWqnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWq3NyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWrHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWrXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWrnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWr3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWsHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWsXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWsnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_wYbXfHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWs3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWtHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWtXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWtnNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_wYbXoXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWt3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWuHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWuXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_wYbXkXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWunNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWu3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWvHNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWvXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWvnNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWv3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWwHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWwXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_wYbXf3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWwnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWw3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWxHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWxXNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWxnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_wYbXlXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWx3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_wYbXrHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWyHNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_wYbXoHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWyXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWynNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWy3NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWzHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWzXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWznNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbWz3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbW0HNyEfCIHPp1rUlC6w" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_wYbW0XNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW0nNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW03NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_wYbXhnNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbW1HNyEfCIHPp1rUlC6w" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_wYbW1XNyEfCIHPp1rUlC6w" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_wYbW1nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW13NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW2HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_wYbXo3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW2XNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW2nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_wYbXkXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW23NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_wYbXn3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW3HNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW3XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW3nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW33NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW4HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_wYbXg3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbW4XNyEfCIHPp1rUlC6w" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_wYbW4nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW43NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_wYbXenNyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbW5HNyEfCIHPp1rUlC6w" elementId="kind" name="Kind"/>
    <parameters xmi:id="_wYbW5XNyEfCIHPp1rUlC6w" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_wYbW5nNyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW53NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_wYbXenNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW6HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW6XNyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW6nNyEfCIHPp1rUlC6w" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW63NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW7HNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW7XNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_wYbXfXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW7nNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW73NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_wYbXq3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW8HNyEfCIHPp1rUlC6w" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW8XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW8nNyEfCIHPp1rUlC6w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_wYbXk3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbW83NyEfCIHPp1rUlC6w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_wYbW9HNyEfCIHPp1rUlC6w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_wYbW9XNyEfCIHPp1rUlC6w" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_remove" commandName="Remove External Analysis" description="Remove External Analysis" category="_wYbXj3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW9nNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_wYbXonNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW93NyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW-HNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW-XNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW-nNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW-3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW_HNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW_XNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW_nNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbW_3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_wYbXjHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXAHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_wYbXe3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXAXNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_wYbXeHNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXAnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_wYbXhnNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXA3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXBHNyEfCIHPp1rUlC6w" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXBXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_wYbXn3NyEfCIHPp1rUlC6w">
    <parameters xmi:id="_wYbXBnNyEfCIHPp1rUlC6w" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_wYbXB3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_wYbXi3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXCHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_wYbXmXNyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXCXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXCnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXC3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXDHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXDXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXDnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXD3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXEHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXEXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXEnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXE3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXFHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXFXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXFnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXF3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXGHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXGXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXGnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXG3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXHHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXHXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXHnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXH3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXIHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXIXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXInNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXI3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXJHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXJXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXJnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXJ3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXKHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXKXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXKnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXK3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXLHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXLXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXLnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXL3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXMHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXMXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXMnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXM3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXNHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXNXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXNnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXN3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXOHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXOXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXOnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXO3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXPHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXPXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXPnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXP3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXQHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXQXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXQnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXQ3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXRHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXRXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXRnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXR3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXSHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.cdt.ui.cview.contribution/org.eclipse.mylyn.cdt.ui.cview.focusActiveTask.action" commandName="Focus on Active Task" description="Focus only on elements in active Mylyn task" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXSXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXSnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXS3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXTHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXTXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXTnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXT3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXUHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXUXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXUnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXU3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXVHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXVXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXVnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXV3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXWHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXWXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXWnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXW3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXXHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXXXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXXnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXX3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXYHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXYXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXYnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXY3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXZHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXZXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXZnNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXZ3NyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXaHNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXaXNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <commands xmi:id="_wYbXanNyEfCIHPp1rUlC6w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_wYbXk3NyEfCIHPp1rUlC6w"/>
  <addons xmi:id="_wYbXa3NyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_wYbXbHNyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_wYbXbXNyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_wYbXbnNyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_wYbXb3NyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_wYbXcHNyEfCIHPp1rUlC6w" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_wYbXcXNyEfCIHPp1rUlC6w" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_wYbXcnNyEfCIHPp1rUlC6w" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_wYbXc3NyEfCIHPp1rUlC6w" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_wYbXdHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_wYbXdXNyEfCIHPp1rUlC6w" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_wYbXdnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_wYbXd3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_wYbXeHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_wYbXeXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_wYbXenNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_wYbXe3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_wYbXfHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_wYbXfXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_wYbXfnNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.changelog" name="Changelog" description="Changelog key bindings"/>
  <categories xmi:id="_wYbXf3NyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_wYbXgHNyEfCIHPp1rUlC6w" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_wYbXgXNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_wYbXgnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.cdt.ui.commands" name="CDT Context" description="CDT Task-Focused Interface Commands"/>
  <categories xmi:id="_wYbXg3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_wYbXhHNyEfCIHPp1rUlC6w" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_wYbXhXNyEfCIHPp1rUlC6w" elementId="rpmlint.category" name="Rpmlint Commands" description="Specfile Editor Commands"/>
  <categories xmi:id="_wYbXhnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_wYbXh3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_wYbXiHNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.packs.ui.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_wYbXiXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.autotools.ui.category.invokeAutotools" name="Invoke Autotools"/>
  <categories xmi:id="_wYbXinNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_wYbXi3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_wYbXjHNyEfCIHPp1rUlC6w" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_wYbXjXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_wYbXjnNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_wYbXj3NyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_wYbXkHNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_wYbXkXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_wYbXknNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_wYbXk3NyEfCIHPp1rUlC6w" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_wYbXlHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_wYbXlXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
  <categories xmi:id="_wYbXlnNyEfCIHPp1rUlC6w" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_wYbXl3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_wYbXmHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_wYbXmXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.ui.category.source" name="Source" description="Source commands"/>
  <categories xmi:id="_wYbXmnNyEfCIHPp1rUlC6w" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_wYbXm3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_wYbXnHNyEfCIHPp1rUlC6w" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_wYbXnXNyEfCIHPp1rUlC6w" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.category" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_wYbXnnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_wYbXn3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_wYbXoHNyEfCIHPp1rUlC6w" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_wYbXoXNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_wYbXonNyEfCIHPp1rUlC6w" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_wYbXo3NyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_wYbXpHNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_wYbXpXNyEfCIHPp1rUlC6w" elementId="rpmEditor.category" name="Editor Commands" description="Specfile Editor Commands"/>
  <categories xmi:id="_wYbXpnNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_wYbXp3NyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_wYbXqHNyEfCIHPp1rUlC6w" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_wYbXqXNyEfCIHPp1rUlC6w" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_wYbXqnNyEfCIHPp1rUlC6w" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_wYbXq3NyEfCIHPp1rUlC6w" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_wYbXrHNyEfCIHPp1rUlC6w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_wYbXrXNyEfCIHPp1rUlC6w" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_wYbXrnNyEfCIHPp1rUlC6w" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_wYbXr3NyEfCIHPp1rUlC6w" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_wYbXsHNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_wYbXsXNyEfCIHPp1rUlC6w" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
</application:Application>
