/*******************************************************************************
** Copyright (c) 2024 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : OS.h
** Module Name : Operating System
** -----------------------------------------------------------------------------
**
** Description : Header file for Operating System module
** This file contains the interface definitions for the OS module
** providing task scheduling and timing management services.
** -----------------------------------------------------------------------------
**
** Documentation reference : 04_System Module Interface Specification.md
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V00.01 07/29/2025
** - Baseline Created
**
*******************************************************************************/

#ifndef OS_H
#define OS_H

/*************************** Inclusion files **********************************/
#include "Std_Types.h"
#include "Gpt.h"
#include "WdgM.h"
#include "EcuM.h"
//#include "Nvm.h"

/********************** Component configuration *******************************/

/**************** Declaration of local symbol and constants *******************/

/******************** Declaration of local macros *****************************/

/* Maximum number of tasks in the system */
#define OS_SCHEDULER_MAX_TASKS                      8u

/* General constants */
#define OS_ZERO                     0u              /* Zero value */
#define OS_ONE                      1u              /* One value */



/********************* Declaration of local types *****************************/

/* System tick type */
typedef uint32 Os_TickType;

/* Task ID enumeration */
typedef enum {
    OS_TASK_SAFETY_MONITOR = 0,                     /* Safety Monitor SWC - 10ms */
    OS_TASK_HEATING_CONTROL,                        /* Heating Control SWC - 100ms */
    OS_TASK_WDGM,                                   /* Watchdog Manager - 20ms */
    OS_TASK_ECUM,                                   /* ECU State Manager - 10ms */
    OS_TASK_COM,                                    /* Communication Manager - 10ms */
    OS_TASK_NVM,                                    /* NvM Manager - 10ms */
    OS_TASK_ADCIF,                                  /* ADC Interface - 5ms */
    OS_TASK_ADC,                                    /* ADC Driver - 1ms */
    OS_TASK_COUNT                                   /* Total number of tasks */
} Os_TaskIdType;

/* Task configuration structure */
typedef struct {
    VAR(Os_TaskIdType, OS_VAR) taskId;              /* Task identifier */
    VAR(uint16, OS_VAR) cyclePeriodMs;              /* Task execution period in ms */
    VAR(uint16, OS_VAR) delayMs;                    /* Task first execution delay in ms */
    VAR(uint32, OS_VAR) nextRunTime;                /* Task next run time in ms */
    VAR(boolean, OS_VAR) enabled;                   /* Task enabled flag */
    FUNC(void, OS_CODE) (*taskFunction)(void);      /* Task function pointer */
} Os_TaskConfigType;

/******************* Declaration of local variables ***************************/

/********************* Declaration of local constants *************************/

/******************** Declaration of exported variables ***********************/

/****************** Declaration of exported constant **************************/

/*******************************************************************************
** FUNCTIONS **
*******************************************************************************/

/**************************************************************************************************
** Function name   : Os_Init
** Description     : OS initialization
** Parameters      : None
** Return value    : None
** Notes           : This function initializes the operating system
**************************************************************************************************/
EXTERN FUNC(void, OS_CODE) Os_Init(void);

/**************************************************************************************************
** Function name   : Os_CreateTask
** Description     : Task management
** Parameters      : taskId - Task identifier
**                   taskConfig - Pointer to task configuration
** Return value    : Std_ReturnType - E_OK: Task created successfully
**                                   E_NOT_OK: Task creation failed
** Notes           : This function creates a task in the system
**************************************************************************************************/
EXTERN FUNC(Std_ReturnType, OS_CODE) Os_CreateTask(VAR(Os_TaskIdType, AUTOMATIC) taskId,
                                             P2CONST(Os_TaskConfigType, AUTOMATIC, OS_APPL_CONST) taskConfig);

/**************************************************************************************************
** Function name   : Os_Schedule
** Description     : Scheduler control
** Parameters      : None
** Return value    : None
** Notes           : This function performs task scheduling
**************************************************************************************************/
EXTERN FUNC(void, OS_CODE) Os_Schedule(void);

/**************************************************************************************************
** Function name   : Os_GetSystemTime
** Description     : Timer and delay services
** Parameters      : systemTime - Pointer to store system time
** Return value    : Std_ReturnType - E_OK: Time retrieved successfully
**                                   E_NOT_OK: Time retrieval failed
** Notes           : This function gets the current system time
**************************************************************************************************/
EXTERN FUNC(Std_ReturnType, OS_CODE) Os_GetSystemTime(P2VAR(Os_TickType, AUTOMATIC, OS_APPL_DATA) systemTime);

#endif /* OS_H */
