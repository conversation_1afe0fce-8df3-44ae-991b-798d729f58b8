eclipse.preferences.version=1
flashDeviceName=Z20K116M
gdb.client.commands=set mem inaccessible-by-default off
gdb.client.executable=${cross_prefix}gdb${cross_suffix}
gdb.client.other=
gdb.jlink.doDebugInRam=false
gdb.jlink.doInitialReset=true
gdb.jlink.doPreRunReset=true
gdb.jlink.enableFlashBreakpoints=true
gdb.jlink.enableSemihosting=true
gdb.jlink.enableSwo=true
gdb.jlink.init.other=
gdb.jlink.initialReset.speed=1000
gdb.jlink.initialReset.type=
gdb.jlink.preRun.other=
gdb.jlink.preRunReset.type=
gdb.jlink.semihosting.client=false
gdb.jlink.semihosting.telnet=true
gdb.jlink.speed=auto
gdb.jlink.swoEnableTarget.cpuFreq=0
gdb.jlink.swoEnableTarget.portMask=0x1
gdb.jlink.swoEnableTarget.swoFreq=0
gdb.server.connection=usb
gdb.server.connection.address=
gdb.server.doStart=true
gdb.server.endianness=little
gdb.server.executable=${jlink_path}/${jlink_gdbserver}
gdb.server.interface=swd
gdb.server.other=-singlerun -strict -timeout 0 -nogui
gdb.server.speed=1000
