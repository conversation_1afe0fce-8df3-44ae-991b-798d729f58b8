/***************************************************************************************************
** Copyright (c) 2018 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
**
** -------------------------------------------------------------------------------------------------
** File Name    : main.c
** Module name  : STAR
** -------------------------------------------------------------------------------------------------
** Description : Interrupt configuration
**
** -------------------------------------------------------------------------------------------------
**
** Documentation reference : EME-19ST004-12227  (SW_LLD_STAR)
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V00.01 S.Todkar 30/09/2024
** - Baseline Created
*******************************************************************************/

/************************** Inclusion files *******************************************************/
#include "platform_cfg.h"
#include "Z20K11xM_drv.h"
//#include "system.h"
#include "Std_Types.h"
#include "Mcu.h"
#include "Gpt.h"
#include "Wdg.h"
#include "Wdgif.h"
#include "Wdgm.h"
#include "OS.h"
#include "EcuM.h"

STATIC FUNC(void, MAIN_CODE) InitAllModules(void);

VAR(uint32, MAIN_VAR) __vector_table;	/* Declaration of interrupt vector table */
/*****************************************************************************
 * Functions: HardFault_Handler
 * Function: Hardware failure interrupts the execution function
 * Parameter:
 * Return:
 * * Functions: HardFault_Handler
 * Function: Hardware failure interrupts the execution function
 *Parameter:
 *Return:
 * Description: 1. Interrupt the execution function, in order to pursue high efficiency, 
 				directly use the function name in the interrupt vector table.
 *      		2,Use frequent interruptions, put in front, reduce the time occupied by judgment.
 *      		3,execute one interrupt task function at a time, reducing various judgments.
 *      		4,If the IRQ interrupt is not used, the corresponding interrupt execution function should be masked.
****************************************************************************/
void HardFault_Handler(void)
{
    /* use for debug */
    /* while(1) CORE_NOP(); */
    NVIC_SystemReset(); /* Software reset */
}
void Delay_ms(unsigned int t)
{
	unsigned int i;
	while (t)
	{
		for(i=0;i<1830;i++) 
			CORE_NOP();	
		t--;	
	}
}
/**************************************************************************************************
** Function name    : main
** Description      : Program starting point
** Parameter index  : None.
** Return value     : None
** Remarks          : None
**************************************************************************************************/
int main(void)
{
    /* Initialize all modules & task schedule*/
    Delay_ms(1000);

    /* Initialize ECU State Manager */
    InitAllModules();

    while(1)
    {
        Os_Schedule();
    }

    return 0;
}

STATIC FUNC(void, MAIN_CODE) InitAllModules(void)
{
    SCB->VTOR = ((uint32_t)&__vector_table) & 0xFFFFFF80U;	/* Clear the last 7 bits of the VTOR register */
	CORE_DSB();	/* Data synchronization, before this instruction, complete all storage processing work. */
	
	CORE_DISABLE_IRQ();	/* Disable global IRQ */

    PMU_Init();
    /* Initialize MCU (clock, PLL, power management) first */
    Mcu_Init();
    /* Initialize General Purpose Timer */
    Gpt_Init();
    /* Initialize Watchdog Driver */
    //Wdg_Init();
    /* Initialize Watchdog Manager */
    //WdgM_Init();
    /* Initialize Operating System */
    Os_Init();
    /* Initialize ECU State Manager */
    //EcuM_Init();

	SYSCTRL_ResetModule(SYSCTRL_HWDIV);
	SYSCTRL_EnableModule(SYSCTRL_HWDIV);
	HWDIV_Init();
	CORE_ENABLE_IRQ();	/* Enable global IRQ */
}

