/***************************************************************************************************
** Copyright (c) 2024 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -------------------------------------------------------------------------------------------------
** File Name   : TTMT.h
** Module Name : TIMT
** -------------------------------------------------------------------------------------------------
**
** Description : Configuration file of component TIMT.c
** This file must exclusively contain informations needed to
** use this component.
** -------------------------------------------------------------------------------------------------
**
** Documentation reference : EME-20ST002-12119.01  (SW_LLD_TIMT)
**
****************************************************************************************************
** R E V I S I O N H I S T O R Y
****************************************************************************************************
** V00.01 Xuening.L 2024/09/04
** - Baseline Created
**
***************************************************************************************************/
/* To avoid multi-inclusions */
#ifndef TIMT_H
#define TIMT_H

/*************************** Inclusion files ******************************************************/
#include "Std_Types.h"
#include "Z20k11xM_stim.h"
#include "Z20K11xM_clock.h"
#include "Z20K11xM_sysctrl.h"

#include "LINT.h"
/**************** Declaration of global symbol and constants **************************************/

/******************** Declaration of global macros ************************************************/

/******************** Declaration of global macros ************************************************/

/********************* Declaration of global types ************************************************/

/******************** External links of global variables ******************************************/

/******************** External links of global constant *******************************************/

/***************************************************************************************************
** FUNCTIONS **
***************************************************************************************************/
/************************** Function definitions **************************************************/

/**************** Declaration of global symbol and constants ******************/
/*******************************************************************************
** Function name	: TIMT_Init
** Description		: Timer Initialization function
** Parameter index 	: None
** Return value		: None
** Remarks			: None
*******************************************************************************/
EXTERN FUNC(void, TIMT_CODE) TIMT_Init(void);

/*******************************************************************************
** Function name	: TIMT_GetSysTime
** Description		: Returns free running timer value
** Parameter index 	: None
** Return value		: TIMT_Cntr
** Remarks          : None
*******************************************************************************/
EXTERN FUNC(uint32, TIMT_CODE) TIMT_GetSysTime(void);

/*******************************************************************************
** Function name	: TIMT_Get1msTrig
** Description		: Returns timer flag
** Parameter index 	: None
** Return value		: Timer flag status
** Remarks          : None
*******************************************************************************/
EXTERN FUNC(BOOLEAN, TIMT_CODE) TIMT_Get1msTrig(void);

/*******************************************************************************
** Function name	: TIMT_Reset1msTrig
** Description		: Reset Timer Flag
** Parameter index 	: None
** Return value		: None
** Remarks          : None
*******************************************************************************/
EXTERN FUNC(void, TIMT_CODE) TIMT_Reset1msTrig(void);

/*******************************************************************************
** Function name    : TIMT_STIM0_ISR
** Description      : Timer 0 Scheduler timer tick ISR
** Parameter index  : None
** Return value     : None
** Remarks          : None
*******************************************************************************/
EXTERN FUNC(void, TIMT_CODE) TIMT_STIM0_ISR(void);

/*******************************************************************************
** Function name    : TIMT_STIM1_ISR
** Description      : Timer 1 Scheduler timer tick ISR
** Parameter index  : None
** Return value     : None
** Remarks          : None
*******************************************************************************/
EXTERN FUNC(void, TIMT_CODE) TIMT_STIM1_ISR(void);


#endif /* TIMT_H */
