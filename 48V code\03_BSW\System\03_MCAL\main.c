/**************************************************************************************************/
/**
 * @file      : main.c
 * @brief     : STIM example file.
 * @version   : V1.8.0
 * @date      : May-2020
 * <AUTHOR> Zhixin Semiconductor
 *
 * @note      : This example contains sample code for customer evaluation purpose only. It is not
 * part of the production code deliverables. The example code is only tested under defined
 * environment with related context of the whole example project. Therefore, it is not guaranteed
 * that the example always works under user environment due to the diversity of hardware and
 * software environment. Do not use pieces copy of the example code without prior and separate
 * verification and validation in user environment.
 * 
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "Z20K11xM_drv.h"
#include "Z20K11xM_clock.h"
#include "Z20K11xM_sysctrl.h"
#include "Z20K11xM_wdog.h"
#include "Z20K11xM_gpio.h"
#include "Z20K11xM_uart.h"
#include "Z20K11xM_srmc.h"
#include "Z20K11xM_pmu.h"
#include "Z20K11xM_stim.h"


static void STIMTEST_IntCallBack(void)
{
    /* toggle*/
    GPIO_TogglePinOutput(PORT_B, GPIO_5);
}

/* STIM configuration */
const STIM_Config_t stimConfig = 
{
    .workMode = STIM_FREE_COUNT,
    .compareValue = 64000,    /*counter clock is 64M, compare value =64000,  period = 1ms*/
    .countResetMode = STIM_INCREASE_FROM_0,
    .clockSource = STIM_FUNCTION_CLOCK,
};

/* Interupt vector table*/
extern uint32_t __vector_table;

void system_init(void)
{
   /* Interrupt vector table redefinition*/
    SCB->VTOR = ((uint32_t)&__vector_table) & 0xFFFFFF00U;
    __DSB();
    
    /* Disable wdog */
    SYSCTRL_EnableModule(SYSCTRL_WDOG);
    WDOG_Disable();

    /* Select FIRC64M as system clock*/
    CLK_SysClkSrc(CLK_SYS_FIRC64M);
    
    /*Config PortB clock, enable PortB module*/
    CLK_ModuleSrc(CLK_PORTB, CLK_SRC_FIRC64M);
    SYSCTRL_EnableModule(SYSCTRL_PORTB);
    /*Config GPIO module*/
    SYSCTRL_EnableModule(SYSCTRL_GPIO);
    
    /*Config PTB5 as GPIO function*/
    PORT_PinmuxConfig(PORT_B, GPIO_5, PTB5_GPIO);
    /*Config PTB5 as GPIO output*/
    GPIO_SetPinDir(PORT_B, GPIO_5, GPIO_OUTPUT);

}

void main()
{
    /* System init */
    system_init();
    
    /* Configure STIM function clock*/
    CLK_ModuleSrc(CLK_STIM, CLK_SRC_FIRC64M);
    
    /* Enable STIM module */
    SYSCTRL_EnableModule(SYSCTRL_STIM);
    
    /* Init STIM_0*/
    STIM_Init(STIM_0,&stimConfig);
    
    /* Install interrupt callback function */
    STIM_InstallCallBackFunc(STIM_0,STIM_INT,STIMTEST_IntCallBack);
    /* Enable STIM_0 interrupt*/
    STIM_IntCmd(STIM_0, ENABLE);
    
    /*Enable STIM*/
    STIM_Enable(STIM_0);
    /* Enable STIM NVIC IRQ*/
    NVIC_EnableIRQ(STIM_IRQn);

    while(1);
}
