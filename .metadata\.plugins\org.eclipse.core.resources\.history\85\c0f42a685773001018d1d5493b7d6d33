/*******************************************************************************
** Copyright (c) 2024 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : OS.c
** Module Name : Operating System
** -----------------------------------------------------------------------------
**
** Description : Implementation file for Operating System module
** This file contains the implementation of the OS module
** providing task scheduling and timing management services.
** -----------------------------------------------------------------------------
**
** Documentation reference : 04_System Module Interface Specification.md
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V00.01 07/29/2025
** - Baseline Created
**
*******************************************************************************/

/*************************** Inclusion files **********************************/
#include "OS.h"

/********************** Component configuration *******************************/

/**************** Declaration of local symbol and constants *******************/

/******************** Declaration of local macros *****************************/

/********************* Declaration of local types *****************************/

/******************* Declaration of local variables ***************************/

/* Task configuration array */
STATIC VAR(Os_TaskConfigType, OS_VAR) Os_Tasks[OS_SCHEDULER_MAX_TASKS];

/* OS initialization status */
STATIC VAR(boolean, OS_VAR) Os_Initialized = FALSE;

/* System time counter */
STATIC VAR(Os_TickType, OS_VAR) Os_SystemTime = OS_ZERO;

/********************* Declaration of local constants *************************/

/* Forward declarations for task functions */
FUNC(void, OS_CODE) SafetyMonitor_Runnable(void)
{

}
FUNC(void, OS_CODE) HeatingControl_Runnable(void)
{

}
FUNC(void, OS_CODE) Com_MainFunction(void)
{

}
FUNC(void, OS_CODE) AdcIf_MainFunction(void)
{

}
FUNC(void, OS_CODE) Adc_MainFunction(void){

}

/* Task configuration table initialization according to specification */
STATIC CONST(Os_TaskConfigType, AUTOMATIC) Os_TaskConfigTable[OS_SCHEDULER_MAX_TASKS] = {
    /* Safety Monitor Task */
    {
        OS_TASK_SAFETY_MONITOR,                     /* taskId */
        10u,                                        /* cyclePeriodMs */
        5u,                                         /* delayMs */
        OS_ZERO,                                    /* nextRunTime */
        TRUE,                                       /* enabled */
        &SafetyMonitor_Runnable                     /* taskFunction */
    },
    /* Heating Control Task */
    {
        OS_TASK_HEATING_CONTROL,                    /* taskId */
        100u,                                       /* cyclePeriodMs */
        5u,                                         /* delayMs */
        OS_ZERO,                                    /* nextRunTime */
        TRUE,                                       /* enabled */
        &HeatingControl_Runnable                    /* taskFunction */
    },
    /* Watchdog Manager Task */
    {
        OS_TASK_WDGM,                               /* taskId */
        20u,                                        /* cyclePeriodMs */
        1u,                                         /* delayMs */
        OS_ZERO,                                    /* nextRunTime */
        TRUE,                                       /* enabled */
        &WdgM_MainFunction                          /* taskFunction */
    },
    /* ECU State Manager Task */
    {
        OS_TASK_ECUM,                               /* taskId */
        10u,                                        /* cyclePeriodMs */
        3u,                                         /* delayMs */
        OS_ZERO,                                    /* nextRunTime */
        TRUE,                                       /* enabled */
        &EcuM_MainFunction                          /* taskFunction */
    },
    /* Communication Task */
    {
        OS_TASK_COM,                                /* taskId */
        10u,                                        /* cyclePeriodMs */
        1u,                                         /* delayMs */
        OS_ZERO,                                    /* nextRunTime */
        TRUE,                                       /* enabled */
        &Com_MainFunction                           /* taskFunction */
    },
    /* NvM Manager Task */
    {
        OS_TASK_NVM,                                /* taskId */
        10u,                                        /* cyclePeriodMs */
        4u,                                         /* delayMs */
        OS_ZERO,                                    /* nextRunTime */
        TRUE,                                       /* enabled */
        &NvM_MainFunction                           /* taskFunction */
    },
    /* ADC Interface Task */
    {
        OS_TASK_ADCIF,                              /* taskId */
        5u,                                         /* cyclePeriodMs */
        1u,                                         /* delayMs */
        OS_ZERO,                                    /* nextRunTime */
        TRUE,                                       /* enabled */
        &AdcIf_MainFunction                         /* taskFunction */
    },
    /* ADC Driver Task */
    {
        OS_TASK_ADC,                                /* taskId */
        1u,                                         /* cyclePeriodMs */
        OS_ZERO,                                    /* delayMs */
        OS_ZERO,                                    /* nextRunTime */
        TRUE,                                       /* enabled */
        &Adc_MainFunction                           /* taskFunction */
    }
};

/******************** Declaration of exported variables ***********************/

/****************** Declaration of exported constant **************************/

/*******************************************************************************
** FUNCTIONS **
*******************************************************************************/

/********************** Internal Function definitions *************************/

/******************** Internal functions declarations *************************/

/************************** Function definitions ******************************/

/**************************************************************************************************
** Function name   : Os_Init
** Description     : OS initialization
** Parameters      : None
** Return value    : None
** Notes           : This function initializes the operating system and task configuration
**************************************************************************************************/
FUNC(void, OS_CODE) Os_Init(void)
{
    VAR(uint8, AUTOMATIC) Os_iTaskIndex = OS_ZERO;

    /* Initialize GPT for system timing */
    (void)Gpt_Init();

    /* Initialize all tasks from configuration table */
    for (Os_iTaskIndex = OS_ZERO; Os_iTaskIndex < OS_SCHEDULER_MAX_TASKS; Os_iTaskIndex++)
    {
        /* Copy task configuration from ROM to RAM */
        Os_Tasks[Os_iTaskIndex].taskId = Os_TaskConfigTable[Os_iTaskIndex].taskId;
        Os_Tasks[Os_iTaskIndex].cyclePeriodMs = Os_TaskConfigTable[Os_iTaskIndex].cyclePeriodMs;
        Os_Tasks[Os_iTaskIndex].delayMs = Os_TaskConfigTable[Os_iTaskIndex].delayMs;
        Os_Tasks[Os_iTaskIndex].nextRunTime = Os_TaskConfigTable[Os_iTaskIndex].delayMs;
        Os_Tasks[Os_iTaskIndex].enabled = Os_TaskConfigTable[Os_iTaskIndex].enabled;
        Os_Tasks[Os_iTaskIndex].taskFunction = Os_TaskConfigTable[Os_iTaskIndex].taskFunction;
    }

    /* Initialize system time */
    Os_SystemTime = OS_ZERO;

    /* Set initialization status */
    Os_Initialized = TRUE;
}

/**************************************************************************************************
** Function name   : Os_CreateTask
** Description     : Task management
** Parameters      : taskId - Task identifier
**                   taskConfig - Pointer to task configuration
** Return value    : Std_ReturnType - E_OK: Task created successfully
**                                   E_NOT_OK: Task creation failed
** Notes           : This function creates or modifies a task in the system
**************************************************************************************************/
FUNC(Std_ReturnType, OS_CODE) Os_CreateTask(VAR(Os_TaskIdType, AUTOMATIC) taskId,
                                             P2CONST(Os_TaskConfigType, AUTOMATIC, OS_APPL_CONST) taskConfig)
{
    VAR(Std_ReturnType, AUTOMATIC) Os_iRetVal = E_NOT_OK;

    /* Check if OS is initialized and parameters are valid */
    if ((Os_Initialized == TRUE) && (taskConfig != NULL_PTR) && (taskId < OS_SCHEDULER_MAX_TASKS))
    {
        /* Copy task configuration */
        Os_Tasks[taskId].taskId = taskConfig->taskId;
        Os_Tasks[taskId].cyclePeriodMs = taskConfig->cyclePeriodMs;
        Os_Tasks[taskId].delayMs = taskConfig->delayMs;
        Os_Tasks[taskId].nextRunTime = Os_SystemTime + taskConfig->delayMs;
        Os_Tasks[taskId].enabled = taskConfig->enabled;
        Os_Tasks[taskId].taskFunction = taskConfig->taskFunction;

        Os_iRetVal = E_OK;
    }

    return Os_iRetVal;
}

/**************************************************************************************************
** Function name   : Os_Schedule
** Description     : Scheduler control
** Parameters      : None
** Return value    : None
** Notes           : This function performs task scheduling based on time-triggered approach
**************************************************************************************************/
FUNC(void, OS_CODE) Os_Schedule(void)
{
    VAR(uint8, AUTOMATIC) Os_iTaskIndex = OS_ZERO;
    VAR(Os_TickType, AUTOMATIC) Os_iCurrentTime = OS_ZERO;

    /* Check if OS is initialized */
    if (Os_Initialized == TRUE)
    {
        /* Get current system time from GPT */
        (void)Os_GetSystemTime(&Os_iCurrentTime);

        /* Check all tasks for execution */
        for (Os_iTaskIndex = OS_ZERO; Os_iTaskIndex < OS_SCHEDULER_MAX_TASKS; Os_iTaskIndex++)
        {
            /* Check if task is enabled and ready to run */
            if ((Os_Tasks[Os_iTaskIndex].enabled == TRUE) &&
                (Os_Tasks[Os_iTaskIndex].taskFunction != NULL_PTR) &&
                (Os_iCurrentTime >= Os_Tasks[Os_iTaskIndex].nextRunTime))
            {
                /* Execute the task */
                Os_Tasks[Os_iTaskIndex].taskFunction();

                /* Update next run time */
                Os_Tasks[Os_iTaskIndex].nextRunTime = Os_iCurrentTime + Os_Tasks[Os_iTaskIndex].cyclePeriodMs;
            }
        }
    }
}

/**************************************************************************************************
** Function name   : Os_GetSystemTime
** Description     : Timer and delay services
** Parameters      : systemTime - Pointer to store system time
** Return value    : Std_ReturnType - E_OK: Time retrieved successfully
**                                   E_NOT_OK: Time retrieval failed
** Notes           : This function gets the current system time from GPT
**************************************************************************************************/
FUNC(Std_ReturnType, OS_CODE) Os_GetSystemTime(P2VAR(Os_TickType, AUTOMATIC, OS_APPL_DATA) systemTime)
{
    VAR(Std_ReturnType, AUTOMATIC) Os_iRetVal = E_NOT_OK;

    /* Check if OS is initialized and parameter is valid */
    if ((Os_Initialized == TRUE) && (systemTime != NULL_PTR))
    {
        /* Update system time from GPT */
        Os_SystemTime = Gpt_GetTimeElapsed();

        /* Return current system time */
        *systemTime = Os_SystemTime;

        Os_iRetVal = E_OK;
    }

    return Os_iRetVal;
}

