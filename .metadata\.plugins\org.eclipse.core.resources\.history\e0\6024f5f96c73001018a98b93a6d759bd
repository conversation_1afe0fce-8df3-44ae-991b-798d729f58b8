/*******************************************************************************
** Copyright (c) 2024 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : TTMT.c
** Module Name : TIMT
** -----------------------------------------------------------------------------
**
** Description : Configuration file of component TIMT.c
** This file must exclusively contain informations needed to
** use this component.
** -----------------------------------------------------------------------------
**
** Documentation reference : EME-20ST002-12119.01  (SW_LLD_TIMT)
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V00.01 Xuening.L 2024/09/04
** - Baseline Created
**
*******************************************************************************/

/*************************** Inclusion files **********************************/
#include "TIMT.h"

/********************** Component configuration *******************************/

/**************** Declaration of local symbol and constants *******************/

/******************** Declaration of local macros *****************************/

/********************* Declaration of local types *****************************/

/******************* Declaration of local variables ***************************/
/* Counter variable for STIM0 interrupt count */
volatile VAR(uint32,TIMT_VAR)  TIMT_STIM0_Int_Counter; 
/* Timer count variable for normal operations */
STATIC VAR(BOOLEAN, TIMT_VAR) TIMT_STIM0_flag;   /* For Timer2 */

/********************* Declaration of local constants *************************/
/* Define global structure variables to store configuration data of STIM module channel 0 */
const STIM_Config_t STIM0_Config =
{
    .workMode = STIM_FREE_COUNT,            /* STIM0 free counting mode */
    .compareValue = 8000,                   /* STIM uses 8M clock source, STIM0 timing period is 1ms */
    .countResetMode = STIM_INCREASE_FROM_0, /* STIM0 reset, start counting from 0 */
    .clockSource = STIM_FUNCTION_CLOCK,     /* STIM0 selects the clock source of the STIM module, which is the clock selected by the function CLK_ModuleSrc */
    .pinSource = STIM_PIN_0,                /* Select the pin of the input signal, which comes from TMU. Valid in pulse counting mode */
    .pinPoalrity = STIM_ACTIVE_HIGH,        /* Select the polarity of the input signal acitve state. Valid in pulse counting mode */
    .prescalerOrFilterValue = STIM_DIV_2,   /* Select the division coefficient/filter value of the input signal */
    .prescalerMode = DISABLE                /* Disable division/filtering. Used as division in free counting mode; used as filtering in pulse counting mode */
};

/* Define global structure variables to store configuration data of STIM module channel 1 */
const STIM_Config_t STIM1_Config =
{
    .workMode = STIM_FREE_COUNT,            /* STIM1 free counting mode */
    .compareValue = 4000,             /* STIM uses 8M clock source, STIM1 timing period is 50us */
    .countResetMode = STIM_INCREASE_FROM_0, /* STIM1 reset, start counting from 0 */
    .clockSource = STIM_FUNCTION_CLOCK,     /* STIM1 selects the clock source of the STIM module, which is the clock selected by the function CLK_ModuleSrc */
    .pinSource = STIM_PIN_0,                /* Select the pin of the input signal, which comes from TMU. Valid in pulse counting mode */
    .pinPoalrity = STIM_ACTIVE_HIGH,        /* Select the polarity of the input signal acitve state. Valid in pulse counting mode */
    .prescalerOrFilterValue = STIM_DIV_2,   /* Select the division coefficient/filter value of the input signal */
    .prescalerMode = DISABLE                /* Disable division/filtering. Used as division in free counting mode; used as filtering in pulse counting mode */
};

const STIM_Config_t STIM2_Config =
{
    .workMode = STIM_FREE_COUNT,            /* STIM2 free counting mode */
    .compareValue = 352,                    /* STIM uses 8M clock source, STIM2 timing period is 44.4us */
    .countResetMode = STIM_INCREASE_FROM_0, /* STIM2 reset, start counting from 0 */
    .clockSource = STIM_FUNCTION_CLOCK,     /* STIM2 selects the clock source of the STIM module */
    .pinSource = STIM_PIN_0,                /* Selection of input signal pins */
    .pinPoalrity = STIM_ACTIVE_HIGH,        /* Selects the polarity of the input signal acitve state. */
    .prescalerOrFilterValue = STIM_DIV_2,   /* Select the crossover coefficient of the input signal */
    .prescalerMode = DISABLE                /* Disable crossover */
};

/******************** Declaration of exported variables ***********************/

/****************** Declaration of exported constant **************************/

/*******************************************************************************
** FUNCTIONS **
*******************************************************************************/

/**************************** Internal functions declarations *****************/
/********************************** Function definitions **********************/

/*******************************************************************************
** Function name    : TIMT_Init
** Description      : Timer Initialization function
** Parameter index  : None
** Return value     : None
** Remarks          : None
*******************************************************************************/
FUNC(void, TIMT_CODE) TIMT_Init(void)
{
    /* Initialising the STIM module */
    CLK_ModuleSrc(CLK_STIM, CLK_SRC_FIRC64M);
    CLK_SetClkDivider(CLK_STIM, CLK_DIV_8);
    SYSCTRL_ResetModule(SYSCTRL_STIM);
    SYSCTRL_EnableModule(SYSCTRL_STIM);

    /* Initialising the channels of the STIM module */
    STIM_Init(STIM_0, &STIM0_Config);
    STIM_Init(STIM_1, &STIM1_Config);
    STIM_Init(STIM_2, &STIM2_Config);

    /* Initialise STIM interrupt */
    STIM_IntCmd(STIM_0, DISABLE);
    STIM_IntCmd(STIM_1, DISABLE);
    STIM_IntCmd(STIM_2, DISABLE);
    STIM_ClearInt(STIM_0);
    STIM_ClearInt(STIM_1);
    STIM_ClearInt(STIM_2);

    STIM_InstallCallBackFunc(STIM_0, STIM_INT, TIMT_STIM0_ISR);     /* Load the interrupt handling function of STIM module channel 0 */
    STIM_IntCmd(STIM_0, ENABLE);                                    /* Enable the interrupt of STIM module channel 0 */

    STIM_InstallCallBackFunc(STIM_1, STIM_INT, TIMT_STIM1_ISR);     /* Load the interrupt handling function of STIM module channel 1 */
    STIM_IntCmd(STIM_1, ENABLE);   


    //STIM_InstallCallBackFunc(STIM_2, STIM_INT, PWMT_STIM_CH2_ISR);  /* Load the interrupt handling function of STIM module channel 2 */
    //STIM_IntCmd(STIM_2, ENABLE);                                    /* Enable the interrupt of STIM module channel 2 */

    NVIC_SetPriority(STIM_IRQn, 0x1);   /* Set the interrupt priority of STIM_IRQn. (High) 0-3 (Low) */
    NVIC_EnableIRQ(STIM_IRQn);          /* Enable the IRQ interrupt of the STIM module */

    /* Initialize global variables */
    TIMT_STIM0_Int_Counter = 0; /* Counter variable for STIM0 interrupt count */
    TIMT_STIM0_flag = FALSE;

    /* Start the module and begin operation */
    STIM_Enable(STIM_0);    /* Enable STIM module channel 0, channel 0 starts running */
    STIM_Enable(STIM_1);    /* Enable STIM module channel 1, channel 1 starts running */
    //STIM_Enable(STIM_2);    /* Enable STIM module channel 2, channel 2 starts running */

    /* Initialisation of PWM clock */
    /* TIM0 */
    CLK_ModuleSrc(CLK_TIM0, CLK_SRC_FIRC64M);
    CLK_SetClkDivider(CLK_TIM0, CLK_DIV_8);
    SYSCTRL_ResetModule(SYSCTRL_TIM0);
    SYSCTRL_EnableModule(SYSCTRL_TIM0);

    /* TIM1 */
    CLK_ModuleSrc(CLK_TIM1, CLK_SRC_FIRC64M);
    CLK_SetClkDivider(CLK_TIM1, CLK_DIV_8);
    SYSCTRL_ResetModule(SYSCTRL_TIM1);
    SYSCTRL_EnableModule(SYSCTRL_TIM1);

    /* TIM2 */
    CLK_ModuleSrc(CLK_TIM2, CLK_SRC_FIRC64M);
    CLK_SetClkDivider(CLK_TIM2, CLK_DIV_8);
    SYSCTRL_ResetModule(SYSCTRL_TIM2);
    SYSCTRL_EnableModule(SYSCTRL_TIM2);
}

/*******************************************************************************
** Function name    : TIMT_STIM0_ISR
** Description      : STIM_CH0 interrupt function
** Parameter index  : None
** Return value     : None
** Remarks          : None
*******************************************************************************/
FUNC(void, TIMT_CODE) TIMT_STIM0_ISR(void)
{
    STIM_ClearInt(STIM_0);

    TIMT_STIM0_Int_Counter++;

    if (TIMT_STIM0_flag == (BOOLEAN)FALSE)
    {
        TIMT_STIM0_flag = TRUE;
    }
    else
    {
        /* Do nothing */
    }
}

